# 火山语音大模型合成API - 完整解决方案

基于火山语音大模型合成API的Python实现，提供Web界面、命令行工具和Python API三种使用方式。

## 🌟 主要特性

### Web应用（推荐）
- 🎨 **现代化界面** - 基于Bootstrap 5的响应式设计
- 🔄 **智能输入切换** - 文本输入和文件上传二选一
- 🎵 **在线播放下载** - 合成后直接播放和下载
- ⚙️ **可视化配置** - 直观的参数调节界面
- 📱 **移动端支持** - 完美适配手机和平板

### 核心功能
- ✅ **40+音色支持** - 包含通用、角色扮演、趣味口音等
- ✅ **多格式输出** - MP3、WAV、PCM、OGG等格式
- ✅ **参数可调节** - 语速、音量、采样率等
- ✅ **批量处理** - 支持多文本批量合成
- ✅ **命令行工具** - 方便的CLI接口
- ✅ **Python API** - 易于集成的编程接口

## 安装依赖

```bash
pip install -r requirements.txt
```

## 配置信息

在使用前，请确保您已经获得了以下配置信息：

```python
appid = "8721493889"
token = "kShhdfvOpfvUATOML5acIOPfRj2Gq03m"
cluster = "volcano_tts"
voice_type = "zh_female_xinlingjitang_moon_bigtts"
```

## 🚀 快速开始

### 1. Web应用（推荐）

```bash
# 启动Web应用
python app.py

# 访问网站
http://localhost:3000
```

**使用方法：**
- 在文本框输入要合成的文本，或上传.txt文件
- 选择音色、格式、语速等参数
- 点击"开始合成"
- 在线播放或下载音频文件

### 2. 命令行使用

```bash
# 基础合成
python volcano_tts_complete.py --text "你好，世界"

# 指定参数
python volcano_tts_complete.py \
    --text "你好，世界" \
    --voice "zh_female_xinlingjitang_moon_bigtts" \
    --encoding "wav" \
    --speed 1.2 \
    --output "hello.wav"

# 从文件读取
python volcano_tts_complete.py --file "text.txt" --output "output.mp3"

# 列出支持的音色
python volcano_tts_complete.py --list-voices
```

### 3. Python API

```python
from volcano_tts_complete import VolcanoTTS

# 创建TTS客户端
tts = VolcanoTTS()

# 基础合成
result = tts.synthesize_text(
    text="你好，这是测试文本",
    output_file="output.mp3"
)

# 自定义参数合成
result = tts.synthesize_text(
    text="这是自定义参数的测试",
    voice_type="zh_female_xinlingjitang_moon_bigtts",
    encoding="mp3",
    speed_ratio=1.2,  # 1.2倍语速
    loudness_ratio=1.1,  # 1.1倍音量
    rate=24000,  # 24kHz采样率
    output_file="custom.mp3"
)

# 批量合成
texts = ["第一段文本", "第二段文本", "第三段文本"]
results = tts.batch_synthesize(texts, output_dir="batch_output")
```

## 文件说明

- `volcano_tts_complete.py` - 完整的TTS解决方案（推荐使用）
- `tts_volcano.py` - 基础HTTP接口实现
- `使用示例.py` - 详细的使用示例
- `test_tts.py` - 功能测试脚本
- `text.txt` - 示例文本文件
- `requirements.txt` - 依赖包列表
- `README.md` - 使用说明文档

## 运行示例

### 完整解决方案（推荐）
```bash
# 基础使用
python volcano_tts_complete.py

# 查看帮助
python volcano_tts_complete.py --help

# 运行使用示例
python 使用示例.py
```

### 基础HTTP接口
```bash
python tts_volcano.py
```

### 功能测试
```bash
python test_tts.py
```

## 参数说明

### 音色类型 (voice_type)
- `zh_female_xinlingjitang_moon_bigtts` - 中文女声（心灵鸡汤月亮）
- `zh_male_M392_conversation_wvae_bigtts` - 中文男声对话
- 更多音色请参考官方文档

### 音频编码格式 (encoding)
- `mp3` - MP3格式（推荐）
- `wav` - WAV格式
- `pcm` - PCM格式
- `ogg_opus` - OGG Opus格式

### 其他参数
- `speed_ratio` - 语速调节 [0.8, 2.0]，默认1.0
- `rate` - 采样率，支持8000/16000/24000，默认24000
- `loudness_ratio` - 音量调节 [0.5, 2.0]，默认1.0

## 错误处理

程序包含完整的错误处理机制：

- 网络请求异常处理
- API错误码处理
- 文件读写异常处理
- WebSocket连接异常处理

## 注意事项

1. **文本长度限制**：单次请求文本长度限制1024字节（UTF-8编码），建议小于300字符
2. **请求频率**：注意控制请求频率，避免超过并发限制
3. **音频格式**：WAV格式不支持流式合成
4. **文件路径**：确保文本文件路径正确，程序会自动检查文件是否存在

## 常见错误及解决方案

1. **quota exceeded** - 用量超限，需要开通正式版或增购用量
2. **concurrency exceeded** - 并发超限，需要减少并发调用
3. **voice_type error** - 音色类型错误，请检查音色代号
4. **authenticate failed** - 鉴权失败，请检查appid和token

## 技术支持

如遇到问题，请参考火山引擎官方文档：
https://www.volcengine.com/docs/6561/1257584
