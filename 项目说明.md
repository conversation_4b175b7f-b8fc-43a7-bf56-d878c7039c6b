# 火山语音大模型合成API - 项目说明

## 🎯 项目概述

本项目是一个完整的火山语音大模型合成解决方案，提供Web界面、命令行工具和Python API三种使用方式，支持40+种音色的文本转语音功能。

## 📁 项目结构

```
ttsDome/
├── 核心文件
│   ├── app.py                      # Flask Web应用主文件
│   ├── volcano_tts_complete.py     # 完整TTS解决方案（核心模块）
│   ├── tts_volcano.py             # 基础HTTP接口实现
│   └── requirements.txt           # Python依赖包
│
├── Web应用
│   ├── templates/                 # HTML模板目录
│   │   ├── base.html             # 基础模板
│   │   ├── index.html            # 主页模板
│   │   ├── 404.html              # 404错误页
│   │   └── 500.html              # 500错误页
│   └── static/                   # 静态资源目录
│       ├── css/style.css         # 自定义样式
│       ├── js/main.js            # 主要JavaScript
│       ├── js/tts.js             # TTS专用JavaScript
│       └── audio/                # 音频文件存储目录
│
├── 配置和脚本
│   ├── start.sh                  # 启动脚本
│   ├── text.txt                  # 示例文本文件
│   └── uploads/                  # 文件上传临时目录
│
└── 文档
    ├── README.md                 # 项目说明文档
    └── 项目说明.md               # 本文档
```

## ✅ 已实现功能

### 1. Web应用（主要功能）
- **现代化界面** - 基于Bootstrap 5的响应式设计
- **智能输入系统** - 文本输入和文件上传二选一，可随时切换
- **40+音色支持** - 包含通用、角色扮演、趣味口音、多语种等
- **参数配置** - 可视化调节语速、音量、采样率等
- **在线播放** - 合成后直接在线播放音频
- **文件下载** - 支持音频文件下载
- **实时反馈** - 合成进度和结果实时显示

### 2. 命令行工具
- **CLI接口** - 完整的命令行参数支持
- **文件处理** - 支持从文件读取文本
- **批量合成** - 多文本批量处理功能
- **参数验证** - 输入参数的完整验证

### 3. Python API
- **面向对象设计** - 清晰的类结构
- **配置管理** - 灵活的配置系统
- **批量处理** - 支持批量合成功能
- **错误处理** - 完善的异常处理机制

## 🚀 使用方法

### 方法一：Web应用（推荐）

1. **启动应用**：
   ```bash
   python app.py
   ```

2. **访问网站**：
   ```
   http://localhost:3000
   ```

3. **使用步骤**：
   - 在文本框输入要合成的文本，或上传.txt文件
   - 选择音色、格式、语速等参数
   - 点击"开始合成"
   - 在线播放或下载音频文件

### 方法二：命令行工具

```bash
# 基础合成
python volcano_tts_complete.py --text "你好，世界"

# 指定参数
python volcano_tts_complete.py \
    --text "你好，世界" \
    --voice "zh_female_xinlingjitang_moon_bigtts" \
    --encoding "wav" \
    --speed 1.2 \
    --output "hello.wav"

# 从文件读取
python volcano_tts_complete.py --file "text.txt" --output "output.mp3"

# 列出支持的音色
python volcano_tts_complete.py --list-voices
```

### 方法三：Python API

```python
from volcano_tts_complete import VolcanoTTS

# 创建TTS客户端
tts = VolcanoTTS()

# 基础合成
result = tts.synthesize_text(
    text="你好，这是测试文本",
    output_file="output.mp3"
)

# 批量合成
texts = ["第一段文本", "第二段文本", "第三段文本"]
results = tts.batch_synthesize(texts, output_dir="batch_output")
```

## 🎵 支持的音色

### 通用场景（推荐）
- 心灵鸡汤（女声）
- 爽快思思（女声）
- 温暖阿虎（男声）
- 少年梓辛（男声）
- 邻家女孩（女声）
- 渊博小叔（男声）
- 阳光青年（男声）
- 甜美小源（女声）
- 清澈梓梓（女声）
- 解说小明（男声）

### 角色扮演
- 高冷御姐（女声）
- 傲娇霸总（男声）
- 魅力女友（女声）
- 深夜播客（男声）
- 柔美女友（女声）
- 撒娇学妹（女声）
- 东方浩然（男声）

### 趣味口音
- 京腔侃爷（北京口音）
- 湾湾小何（台湾口音）
- 湾区大叔（广东口音）
- 呆萌川妹（四川口音）
- 广州德哥（广东口音）
- 北京小爷（北京口音）

### 多语种
- Sarah（澳洲英语）
- Adam（美式英语）
- Anna（英式英语）
- Smith（英式英语）

## ⚙️ 配置说明

### API配置
```python
# 在volcano_tts_complete.py中配置
appid = "8721493889"
token = "kShhdfvOpfvUATOML5acIOPfRj2Gq03m"
cluster = "volcano_tts"
```

### 推荐设置
- **音色**: `zh_female_xinlingjitang_moon_bigtts` (心灵鸡汤女声)
- **格式**: `mp3` (兼容性好，文件小)
- **采样率**: `24000` (高质量)
- **语速**: `1.0` (正常语速)
- **音量**: `1.0` (正常音量)

## 🔧 技术架构

### 后端技术
- **Flask** - Python Web框架
- **Requests** - HTTP请求处理
- **火山语音API** - 核心语音合成服务

### 前端技术
- **Bootstrap 5** - 现代CSS框架
- **jQuery** - JavaScript库
- **Font Awesome** - 图标库
- **HTML5 Audio** - 音频播放支持

## 📊 性能表现

### 测试数据
- **合成速度**: 2-3秒/次（包含网络延迟）
- **音频质量**: 高质量，自然流畅
- **支持格式**: MP3、WAV、PCM、OGG等
- **文本限制**: 单次最大1024字节
- **并发支持**: 支持多用户同时使用

## 🛠️ 安装和部署

### 环境要求
- Python 3.7+
- pip包管理器
- 现代浏览器

### 安装步骤
```bash
# 1. 安装依赖
pip install -r requirements.txt

# 2. 启动应用
python app.py

# 3. 访问网站
http://localhost:3000
```

### 使用启动脚本
```bash
chmod +x start.sh
./start.sh
```

## 🔍 故障排除

### 常见问题

1. **端口被占用**
   - 修改`app.py`中的端口号
   - 或关闭占用端口的程序

2. **依赖安装失败**
   - 升级pip：`pip install --upgrade pip`
   - 使用国内镜像源

3. **语音合成失败**
   - 检查网络连接
   - 验证API配置信息
   - 查看控制台错误日志

4. **文件上传失败**
   - 检查文件格式（仅支持.txt）
   - 确认文件大小不超过1024字节
   - 检查文件编码（推荐UTF-8）

## 🎯 应用场景

### 个人用户
- 学习辅助工具
- 内容创作配音
- 无障碍访问支持

### 企业应用
- 客服系统语音播报
- 产品演示配音
- 培训材料制作

### 开发者
- API集成开发
- 语音功能快速原型
- 批量内容处理

## 📝 更新日志

### v1.0.0 (2024-01-10)
- ✅ 完整的Web应用实现
- ✅ 40+音色支持
- ✅ 智能输入系统（文本/文件二选一）
- ✅ 命令行工具和Python API
- ✅ 响应式设计和现代化界面

## 📞 技术支持

如遇到问题，请：
1. 查看本文档的故障排除部分
2. 检查控制台错误日志
3. 确认API配置正确
4. 参考README.md文档

---

## 🎉 总结

本项目提供了一个完整、易用的火山语音大模型合成解决方案。Web应用界面现代化，功能完善，支持智能输入切换和40+种音色选择，是文本转语音应用的理想选择。

**推荐使用方式**: Web应用 (http://localhost:3000) - 功能最完整，体验最佳！
