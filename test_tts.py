#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
火山语音大模型合成API测试脚本
"""

import os
import asyncio
from tts_volcano import VolcanoTTS
from tts_volcano_websocket import VolcanoTTSWebSocket


def test_http_tts():
    """测试HTTP接口"""
    print("=" * 50)
    print("测试HTTP接口")
    print("=" * 50)
    
    # 配置信息
    appid = "8721493889"
    token = "kShhdfvOpfvUATOML5acIOPfRj2Gq03m"
    cluster = "volcano_tts"
    voice_type = "zh_female_xinlingjitang_moon_bigtts"
    
    # 测试文本
    test_text = "你好，这是火山语音大模型HTTP接口的测试。今天天气很好，适合出门散步。"
    
    try:
        # 创建TTS客户端
        tts_client = VolcanoTTS(appid, token, cluster)
        
        print(f"测试文本: {test_text}")
        print("开始HTTP合成...")
        
        # 合成语音
        audio_data = tts_client.synthesize_text(
            text=test_text,
            voice_type=voice_type,
            encoding="mp3",
            speed_ratio=1.0,
            rate=24000,
            output_file="test_http_output.mp3"
        )
        
        print(f"HTTP合成成功! 音频大小: {len(audio_data)} 字节")
        return True
        
    except Exception as e:
        print(f"HTTP合成失败: {str(e)}")
        return False


async def test_websocket_tts():
    """测试WebSocket接口"""
    print("=" * 50)
    print("测试WebSocket流式接口")
    print("=" * 50)
    
    # 配置信息
    appid = "8721493889"
    token = "kShhdfvOpfvUATOML5acIOPfRj2Gq03m"
    cluster = "volcano_tts"
    voice_type = "zh_female_xinlingjitang_moon_bigtts"
    
    # 测试文本（稍长一些，适合流式合成）
    test_text = """
    欢迎使用火山语音大模型WebSocket流式合成接口。
    这是一个功能强大的语音合成服务，支持实时流式输出。
    通过WebSocket连接，我们可以边合成边接收音频数据，
    大大提高了用户体验，特别适合长文本的语音合成场景。
    """
    
    try:
        # 创建WebSocket TTS客户端
        tts_client = VolcanoTTSWebSocket(appid, token, cluster)
        
        print(f"测试文本: {test_text.strip()}")
        print("开始WebSocket流式合成...")
        
        # 流式合成语音
        await tts_client.synthesize_to_file(
            text=test_text.strip(),
            output_file="test_websocket_output.mp3",
            voice_type=voice_type,
            encoding="mp3",
            speed_ratio=1.0,
            rate=24000
        )
        
        print("WebSocket流式合成成功!")
        return True
        
    except Exception as e:
        print(f"WebSocket合成失败: {str(e)}")
        return False


def test_file_reading():
    """测试文件读取功能"""
    print("=" * 50)
    print("测试文件读取功能")
    print("=" * 50)
    
    # 测试不同的文件路径
    test_paths = [
        "/Users/<USER>/Downloads/tts/text.txt",
        "text.txt"
    ]
    
    for path in test_paths:
        if os.path.exists(path):
            try:
                with open(path, "r", encoding="utf-8") as f:
                    content = f.read().strip()
                print(f"成功读取文件 {path}")
                print(f"文件内容: {content}")
                print(f"文件大小: {len(content)} 字符")
                return content
            except Exception as e:
                print(f"读取文件 {path} 失败: {str(e)}")
        else:
            print(f"文件 {path} 不存在")
    
    print("所有测试文件都不存在，将使用默认文本")
    return "这是默认的测试文本，用于验证TTS功能。"


async def main():
    """主测试函数"""
    print("火山语音大模型合成API - 功能测试")
    print("=" * 60)
    
    # 测试文件读取
    text_content = test_file_reading()
    
    # 测试HTTP接口
    http_success = test_http_tts()
    
    # 测试WebSocket接口
    websocket_success = await test_websocket_tts()
    
    # 输出测试结果
    print("=" * 60)
    print("测试结果汇总:")
    print(f"HTTP接口测试: {'✅ 成功' if http_success else '❌ 失败'}")
    print(f"WebSocket接口测试: {'✅ 成功' if websocket_success else '❌ 失败'}")
    
    # 检查输出文件
    output_files = ["test_http_output.mp3", "test_websocket_output.mp3"]
    print("\n输出文件检查:")
    for file in output_files:
        if os.path.exists(file):
            size = os.path.getsize(file)
            print(f"{file}: ✅ 存在 ({size} 字节)")
        else:
            print(f"{file}: ❌ 不存在")
    
    print("=" * 60)
    print("测试完成!")


if __name__ == "__main__":
    asyncio.run(main())
