#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
火山语音大模型合成API - 完整解决方案
支持HTTP接口，包含完整的错误处理和配置管理
"""

import json
import base64
import uuid
import requests
import os
import argparse
import time
from typing import Optional, Dict, Any
from pathlib import Path


class VolcanoTTSConfig:
    """TTS配置类"""
    
    def __init__(self):
        self.appid = "8721493889"
        self.token = "kShhdfvOpfvUATOML5acIOPfRj2Gq03m"
        self.cluster = "volcano_tts"
        self.url = "https://openspeech.bytedance.com/api/v1/tts"
        
        # 默认音频参数
        self.default_voice_type = "zh_female_xinlingjitang_moon_bigtts"
        self.default_encoding = "mp3"
        self.default_speed_ratio = 1.0
        self.default_rate = 24000
        self.default_loudness_ratio = 1.0
        
        # 支持的音色列表（基于官方文档）
        self.supported_voices = {
            # 通用场景 - 推荐音色
            "zh_female_xinlingjitang_moon_bigtts": "心灵鸡汤（女声）",
            "zh_female_shuangkuaisisi_moon_bigtts": "爽快思思（女声）",
            "zh_male_wennuanahu_moon_bigtts": "温暖阿虎（男声）",
            "zh_male_shaonianzixin_moon_bigtts": "少年梓辛（男声）",
            "zh_female_linjianvhai_moon_bigtts": "邻家女孩（女声）",
            "zh_male_yuanboxiaoshu_moon_bigtts": "渊博小叔（男声）",
            "zh_male_yangguangqingnian_moon_bigtts": "阳光青年（男声）",
            "zh_female_tianmeixiaoyuan_moon_bigtts": "甜美小源（女声）",
            "zh_female_qingchezizi_moon_bigtts": "清澈梓梓（女声）",
            "zh_male_jieshuoxiaoming_moon_bigtts": "解说小明（男声）",
            "zh_female_kailangjiejie_moon_bigtts": "开朗姐姐（女声）",
            "zh_male_linjiananhai_moon_bigtts": "邻家男孩（男声）",
            "zh_female_tianmeiyueyue_moon_bigtts": "甜美悦悦（女声）",
            "zh_female_qinqienvsheng_moon_bigtts": "亲切女声（女声）",

            # 角色扮演
            "zh_female_gaolengyujie_moon_bigtts": "高冷御姐（女声）",
            "zh_male_aojiaobazong_moon_bigtts": "傲娇霸总（男声）",
            "zh_female_meilinvyou_moon_bigtts": "魅力女友（女声）",
            "zh_male_shenyeboke_moon_bigtts": "深夜播客（男声）",
            "zh_female_sajiaonvyou_moon_bigtts": "柔美女友（女声）",
            "zh_female_yuanqinvyou_moon_bigtts": "撒娇学妹（女声）",
            "zh_male_dongfanghaoran_moon_bigtts": "东方浩然（男声）",

            # 趣味口音
            "zh_male_jingqiangkanye_moon_bigtts": "京腔侃爷（北京口音）",
            "zh_female_wanwanxiaohe_moon_bigtts": "湾湾小何（台湾口音）",
            "zh_female_wanqudashu_moon_bigtts": "湾区大叔（广东口音）",
            "zh_female_daimengchuanmei_moon_bigtts": "呆萌川妹（四川口音）",
            "zh_male_guozhoudege_moon_bigtts": "广州德哥（广东口音）",
            "zh_male_beijingxiaoye_moon_bigtts": "北京小爷（北京口音）",

            # 视频配音
            "zh_male_M100_conversation_wvae_bigtts": "悠悠君子（男声）",
            "zh_female_maomao_conversation_wvae_bigtts": "文静毛毛（女声）",
            "zh_male_xudong_conversation_wvae_bigtts": "快乐小东（男声）",

            # 多语种
            "en_female_sarah_mars_bigtts": "Sarah（澳洲英语）",
            "en_male_adam_mars_bigtts": "Adam（美式英语）",
            "en_female_anna_mars_bigtts": "Anna（英式英语）",
            "en_male_smith_mars_bigtts": "Smith（英式英语）"
        }
        
        # 支持的编码格式
        self.supported_encodings = ["mp3", "wav", "pcm", "ogg_opus"]


class VolcanoTTS:
    """火山语音大模型合成API客户端"""
    
    def __init__(self, config: Optional[VolcanoTTSConfig] = None):
        """
        初始化TTS客户端
        
        Args:
            config: TTS配置对象，如果为None则使用默认配置
        """
        self.config = config or VolcanoTTSConfig()
        self.headers = {
            "Authorization": f"Bearer;{self.config.token}",
            "Content-Type": "application/json"
        }
    
    def synthesize_text(
        self,
        text: str,
        voice_type: Optional[str] = None,
        encoding: Optional[str] = None,
        speed_ratio: Optional[float] = None,
        rate: Optional[int] = None,
        loudness_ratio: Optional[float] = None,
        output_file: Optional[str] = None,
        enable_timestamp: bool = False
    ) -> Dict[str, Any]:
        """
        合成语音
        
        Args:
            text: 要合成的文本
            voice_type: 音色类型
            encoding: 音频编码格式
            speed_ratio: 语速 [0.8, 2.0]
            rate: 音频采样率
            loudness_ratio: 音量调节 [0.5, 2.0]
            output_file: 输出文件路径
            enable_timestamp: 是否启用时间戳
            
        Returns:
            包含音频数据和元信息的字典
        """
        # 使用默认值
        voice_type = voice_type or self.config.default_voice_type
        encoding = encoding or self.config.default_encoding
        speed_ratio = speed_ratio or self.config.default_speed_ratio
        rate = rate or self.config.default_rate
        loudness_ratio = loudness_ratio or self.config.default_loudness_ratio
        
        # 验证参数
        self._validate_parameters(text, voice_type, encoding, speed_ratio, rate, loudness_ratio)
        
        # 生成唯一的请求ID
        reqid = str(uuid.uuid4())
        
        # 构建请求参数
        payload = {
            "app": {
                "appid": self.config.appid,
                "token": self.config.token,
                "cluster": self.config.cluster
            },
            "user": {
                "uid": "user_001"
            },
            "audio": {
                "voice_type": voice_type,
                "encoding": encoding,
                "speed_ratio": speed_ratio,
                "rate": rate,
                "loudness_ratio": loudness_ratio
            },
            "request": {
                "reqid": reqid,
                "text": text,
                "operation": "query"
            }
        }
        
        # 添加时间戳支持
        if enable_timestamp:
            payload["request"]["with_timestamp"] = 1
        
        try:
            print(f"开始合成语音...")
            print(f"文本: {text[:50]}{'...' if len(text) > 50 else ''}")
            print(f"音色: {voice_type}")
            print(f"格式: {encoding}")
            
            start_time = time.time()
            
            # 发送请求
            response = requests.post(
                self.config.url,
                headers=self.headers,
                data=json.dumps(payload),
                timeout=30
            )
            
            # 检查HTTP状态码
            response.raise_for_status()
            
            # 解析响应
            result = response.json()
            
            # 检查业务状态码
            if result.get("code") != 3000:
                raise Exception(f"TTS合成失败: {result.get('message', '未知错误')}")
            
            # 解码音频数据
            audio_data = base64.b64decode(result["data"])
            
            # 计算合成时间
            synthesis_time = time.time() - start_time
            
            # 获取音频信息
            duration = result.get("addition", {}).get("duration", 0)

            # 确保duration_ms是数字类型
            if isinstance(duration, str):
                try:
                    duration_ms = int(duration)
                except (ValueError, TypeError):
                    duration_ms = 0
            else:
                duration_ms = duration if isinstance(duration, (int, float)) else 0

            # 构建返回结果
            synthesis_result = {
                "success": True,
                "audio_data": audio_data,
                "duration_ms": duration_ms,
                "synthesis_time_s": round(synthesis_time, 2),
                "audio_size_bytes": len(audio_data),
                "reqid": reqid,
                "voice_type": voice_type,
                "encoding": encoding,
                "text_length": len(text)
            }
            
            # 如果指定了输出文件，则保存到文件
            if output_file:
                output_path = Path(output_file)
                output_path.parent.mkdir(parents=True, exist_ok=True)
                
                with open(output_path, "wb") as f:
                    f.write(audio_data)
                
                synthesis_result["output_file"] = str(output_path.absolute())
                print(f"音频已保存到: {output_path.absolute()}")
            
            print(f"合成成功! 音频时长: {duration_ms}ms, 合成耗时: {synthesis_time:.2f}s")
            
            return synthesis_result
            
        except requests.exceptions.RequestException as e:
            raise Exception(f"网络请求失败: {str(e)}")
        except json.JSONDecodeError as e:
            raise Exception(f"响应解析失败: {str(e)}")
        except Exception as e:
            raise Exception(f"TTS合成失败: {str(e)}")
    
    def _validate_parameters(self, text: str, voice_type: str, encoding: str, 
                           speed_ratio: float, rate: int, loudness_ratio: float):
        """验证参数"""
        if not text or not text.strip():
            raise ValueError("文本不能为空")
        
        if len(text.encode('utf-8')) > 1024:
            raise ValueError("文本长度超过1024字节限制")
        
        if voice_type not in self.config.supported_voices:
            raise ValueError(f"不支持的音色: {voice_type}")
        
        if encoding not in self.config.supported_encodings:
            raise ValueError(f"不支持的编码格式: {encoding}")
        
        if not (0.8 <= speed_ratio <= 2.0):
            raise ValueError("语速必须在0.8-2.0之间")
        
        if rate not in [8000, 16000, 24000]:
            raise ValueError("采样率必须是8000、16000或24000")
        
        if not (0.5 <= loudness_ratio <= 2.0):
            raise ValueError("音量必须在0.5-2.0之间")
    
    def batch_synthesize(self, texts: list, output_dir: str = "output", **kwargs) -> list:
        """
        批量合成语音
        
        Args:
            texts: 文本列表
            output_dir: 输出目录
            **kwargs: 其他合成参数
            
        Returns:
            合成结果列表
        """
        results = []
        output_path = Path(output_dir)
        output_path.mkdir(parents=True, exist_ok=True)
        
        for i, text in enumerate(texts):
            try:
                output_file = output_path / f"audio_{i+1:03d}.{kwargs.get('encoding', 'mp3')}"
                result = self.synthesize_text(text, output_file=str(output_file), **kwargs)
                results.append(result)
                print(f"完成 {i+1}/{len(texts)}")
            except Exception as e:
                print(f"第{i+1}个文本合成失败: {str(e)}")
                results.append({"success": False, "error": str(e), "text": text})
        
        return results

    def split_long_text(self, text: str, max_bytes: int = 1024) -> list:
        """
        分割长文本

        Args:
            text: 要分割的文本
            max_bytes: 每段最大字节数

        Returns:
            分割后的文本段落列表
        """
        if len(text.encode('utf-8')) <= max_bytes:
            return [text]

        segments = []
        # 按句子分割
        import re
        sentences = re.split(r'([。！？；.!?;])', text)
        current_segment = ''

        for i in range(0, len(sentences), 2):
            sentence = sentences[i] if i < len(sentences) else ''
            punctuation = sentences[i + 1] if i + 1 < len(sentences) else ''
            full_sentence = sentence + punctuation

            test_segment = current_segment + full_sentence
            if len(test_segment.encode('utf-8')) > max_bytes and current_segment:
                segments.append(current_segment.strip())
                current_segment = full_sentence
            else:
                current_segment = test_segment

        if current_segment.strip():
            segments.append(current_segment.strip())

        return [seg for seg in segments if seg]

    def synthesize_long_text(self, text: str, output_dir: str = "long_text_output", **kwargs) -> dict:
        """
        合成长文本（自动分段）

        Args:
            text: 长文本
            output_dir: 输出目录
            **kwargs: 其他合成参数

        Returns:
            合成结果
        """
        segments = self.split_long_text(text)

        if len(segments) == 1:
            # 如果不需要分段，直接合成
            return self.synthesize_text(text=text, **kwargs)

        # 创建输出目录
        output_path = Path(output_dir)
        output_path.mkdir(parents=True, exist_ok=True)

        print(f"长文本将分为 {len(segments)} 段处理...")

        results = []
        total_duration = 0
        encoding = kwargs.get('encoding', 'mp3')

        for i, segment in enumerate(segments):
            output_file = output_path / f"segment_{i+1:03d}.{encoding}"
            try:
                result = self.synthesize_text(
                    text=segment,
                    output_file=str(output_file),
                    **kwargs
                )
                results.append(result)
                total_duration += result.get('duration_ms', 0)
                print(f"✓ 第 {i+1}/{len(segments)} 段合成完成")
            except Exception as e:
                print(f"✗ 第 {i+1}/{len(segments)} 段合成失败: {str(e)}")
                raise e

        return {
            'success': True,
            'segments': results,
            'segments_count': len(segments),
            'total_duration_ms': total_duration,
            'text_length': len(text),
            'voice_type': kwargs.get('voice_type', self.voice_type),
            'encoding': encoding,
            'output_dir': str(output_path.absolute())
        }

    def get_voice_list(self) -> Dict[str, str]:
        """获取支持的音色列表"""
        return self.config.supported_voices.copy()


def main():
    """主函数 - 命令行接口"""
    parser = argparse.ArgumentParser(description="火山语音大模型合成API")
    parser.add_argument("--text", "-t", help="要合成的文本")
    parser.add_argument("--file", "-f", help="包含文本的文件路径")
    parser.add_argument("--output", "-o", help="输出音频文件路径")
    parser.add_argument("--voice", "-v", help="音色类型")
    parser.add_argument("--encoding", "-e", help="音频编码格式", choices=["mp3", "wav", "pcm", "ogg_opus"])
    parser.add_argument("--speed", "-s", type=float, help="语速 (0.8-2.0)")
    parser.add_argument("--rate", "-r", type=int, help="采样率", choices=[8000, 16000, 24000])
    parser.add_argument("--volume", type=float, help="音量 (0.5-2.0)")
    parser.add_argument("--timestamp", action="store_true", help="启用时间戳")
    parser.add_argument("--list-voices", action="store_true", help="列出支持的音色")
    
    args = parser.parse_args()
    
    # 创建TTS客户端
    tts_client = VolcanoTTS()
    
    # 列出音色
    if args.list_voices:
        print("支持的音色列表:")
        for voice_id, voice_name in tts_client.get_voice_list().items():
            print(f"  {voice_id}: {voice_name}")
        return
    
    # 获取文本
    text = None
    if args.text:
        text = args.text
    elif args.file:
        if os.path.exists(args.file):
            with open(args.file, "r", encoding="utf-8") as f:
                text = f.read().strip()
        else:
            print(f"文件不存在: {args.file}")
            return
    else:
        # 尝试从默认路径读取
        default_paths = [
            "/Users/<USER>/Downloads/tts/text.txt",
            "text.txt"
        ]
        for path in default_paths:
            if os.path.exists(path):
                with open(path, "r", encoding="utf-8") as f:
                    text = f.read().strip()
                print(f"从 {path} 读取文本")
                break
        
        if not text:
            text = "你好，这是火山语音大模型合成的测试音频。"
            print("使用默认测试文本")
    
    if not text:
        print("错误: 没有找到要合成的文本")
        return
    
    try:
        # 合成参数
        kwargs = {}
        if args.voice:
            kwargs["voice_type"] = args.voice
        if args.encoding:
            kwargs["encoding"] = args.encoding
        if args.speed:
            kwargs["speed_ratio"] = args.speed
        if args.rate:
            kwargs["rate"] = args.rate
        if args.volume:
            kwargs["loudness_ratio"] = args.volume
        if args.timestamp:
            kwargs["enable_timestamp"] = True
        
        # 输出文件
        output_file = args.output or f"output.{kwargs.get('encoding', 'mp3')}"
        
        # 合成语音
        result = tts_client.synthesize_text(text, output_file=output_file, **kwargs)
        
        print("\n合成完成!")
        print(f"音频文件: {result.get('output_file', 'N/A')}")
        print(f"音频大小: {result['audio_size_bytes']} 字节")
        print(f"音频时长: {result['duration_ms']}ms")
        print(f"合成耗时: {result['synthesis_time_s']}秒")
        
    except Exception as e:
        print(f"错误: {str(e)}")


if __name__ == "__main__":
    main()
