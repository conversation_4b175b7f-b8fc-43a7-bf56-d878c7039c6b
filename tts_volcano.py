#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
火山语音大模型合成API - 文本转语音
使用HTTP接口实现文本转语音功能
"""

import json
import base64
import uuid
import requests
import os
from typing import Optional


class VolcanoTTS:
    """火山语音大模型合成API客户端"""
    
    def __init__(self, appid: str, token: str, cluster: str = "volcano_tts"):
        """
        初始化TTS客户端
        
        Args:
            appid: 应用标识
            token: 应用令牌
            cluster: 业务集群，默认为volcano_tts
        """
        self.appid = appid
        self.token = token
        self.cluster = cluster
        self.url = "https://openspeech.bytedance.com/api/v1/tts"
        self.headers = {
            "Authorization": f"Bearer;{token}",
            "Content-Type": "application/json"
        }
    
    def synthesize_text(
        self,
        text: str,
        voice_type: str = "zh_female_xinlingjitang_moon_bigtts",
        encoding: str = "mp3",
        speed_ratio: float = 1.0,
        rate: int = 24000,
        output_file: Optional[str] = None
    ) -> bytes:
        """
        合成语音
        
        Args:
            text: 要合成的文本
            voice_type: 音色类型
            encoding: 音频编码格式 (wav/pcm/ogg_opus/mp3)
            speed_ratio: 语速 [0.8, 2.0]
            rate: 音频采样率 (8000/16000/24000)
            output_file: 输出文件路径，如果不指定则返回音频数据
            
        Returns:
            音频数据的字节流
        """
        # 生成唯一的请求ID
        reqid = str(uuid.uuid4())
        
        # 构建请求参数
        payload = {
            "app": {
                "appid": self.appid,
                "token": self.token,
                "cluster": self.cluster
            },
            "user": {
                "uid": "user_001"
            },
            "audio": {
                "voice_type": voice_type,
                "encoding": encoding,
                "speed_ratio": speed_ratio,
                "rate": rate
            },
            "request": {
                "reqid": reqid,
                "text": text,
                "operation": "query"
            }
        }
        
        try:
            # 发送请求
            response = requests.post(
                self.url,
                headers=self.headers,
                data=json.dumps(payload),
                timeout=30
            )
            
            # 检查HTTP状态码
            response.raise_for_status()
            
            # 解析响应
            result = response.json()
            
            # 检查业务状态码
            if result.get("code") != 3000:
                raise Exception(f"TTS合成失败: {result.get('message', '未知错误')}")
            
            # 解码音频数据
            audio_data = base64.b64decode(result["data"])
            
            # 如果指定了输出文件，则保存到文件
            if output_file:
                with open(output_file, "wb") as f:
                    f.write(audio_data)
                print(f"音频已保存到: {output_file}")
            
            # 打印音频信息
            duration = result.get("addition", {}).get("duration", "未知")
            print(f"合成成功! 音频时长: {duration}ms")
            
            return audio_data
            
        except requests.exceptions.RequestException as e:
            raise Exception(f"网络请求失败: {str(e)}")
        except json.JSONDecodeError as e:
            raise Exception(f"响应解析失败: {str(e)}")
        except Exception as e:
            raise Exception(f"TTS合成失败: {str(e)}")


def main():
    """主函数 - 演示如何使用TTS功能"""
    
    # 配置信息
    appid = "8721493889"
    token = "kShhdfvOpfvUATOML5acIOPfRj2Gq03m"
    cluster = "volcano_tts"
    voice_type = "zh_female_xinlingjitang_moon_bigtts"
    
    # 文本文件路径
    text_file_path = "/Users/<USER>/Downloads/tts/text.txt"
    
    try:
        # 读取文本内容
        if os.path.exists(text_file_path):
            with open(text_file_path, "r", encoding="utf-8") as f:
                text = f.read().strip()
        else:
            # 如果文件不存在，使用默认文本
            text = "你好，这是火山语音大模型合成的测试音频。"
            print(f"文件 {text_file_path} 不存在，使用默认文本")
        
        if not text:
            print("文本内容为空，请检查文件内容")
            return
        
        print(f"要合成的文本: {text}")
        print(f"文本长度: {len(text)} 字符")
        
        # 创建TTS客户端
        tts_client = VolcanoTTS(appid, token, cluster)
        
        # 生成输出文件名
        output_file = "output_audio.mp3"
        
        # 合成语音
        print("开始合成语音...")
        audio_data = tts_client.synthesize_text(
            text=text,
            voice_type=voice_type,
            encoding="mp3",
            speed_ratio=1.0,
            rate=24000,
            output_file=output_file
        )
        
        print(f"音频数据大小: {len(audio_data)} 字节")
        print("语音合成完成!")
        
    except Exception as e:
        print(f"错误: {str(e)}")


if __name__ == "__main__":
    main()
