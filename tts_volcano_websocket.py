#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
火山语音大模型合成API - WebSocket流式合成
支持实时流式语音合成
"""

import json
import base64
import uuid
import struct
import asyncio
import websockets
import os
from typing import Optional, AsyncGenerator


class VolcanoTTSWebSocket:
    """火山语音大模型WebSocket流式合成客户端"""
    
    def __init__(self, appid: str, token: str, cluster: str = "volcano_tts"):
        """
        初始化WebSocket TTS客户端
        
        Args:
            appid: 应用标识
            token: 应用令牌
            cluster: 业务集群，默认为volcano_tts
        """
        self.appid = appid
        self.token = token
        self.cluster = cluster
        self.ws_url = "wss://openspeech.bytedance.com/api/v1/tts/ws_binary"
        self.headers = {
            "Authorization": f"Bearer;{token}"
        }
    
    def _create_binary_message(self, payload: dict) -> bytes:
        """
        创建二进制协议消息

        Args:
            payload: JSON负载数据

        Returns:
            二进制消息数据
        """
        # 将payload转换为JSON字符串并编码为UTF-8
        json_data = json.dumps(payload).encode('utf-8')

        # 构建消息头 (4字节)
        # Protocol version (4 bits): 0001 = 1
        # Header size (4 bits): 0001 = 1 (4 bytes)
        # Message type (4 bits): 0001 = 1 (full client request)
        # Message type specific flags (4 bits): 0000 = 0
        # Message serialization method (4 bits): 0001 = 1 (JSON)
        # Message compression (4 bits): 0000 = 0 (no compression)
        # Reserved (8 bits): 00000000 = 0

        # 按位构建32位头部
        protocol_version = 1  # 4 bits
        header_size = 1       # 4 bits
        message_type = 1      # 4 bits
        flags = 0            # 4 bits
        serialization = 1    # 4 bits
        compression = 0      # 4 bits
        reserved = 0         # 8 bits

        # 组合成32位整数
        header_value = (
            (protocol_version << 28) |
            (header_size << 24) |
            (message_type << 20) |
            (flags << 16) |
            (serialization << 12) |
            (compression << 8) |
            reserved
        )

        # 打包为大端序4字节
        header = struct.pack('>I', header_value)

        print(f"发送消息头: 0x{header_value:08X}")
        print(f"JSON数据长度: {len(json_data)} 字节")

        # 返回完整消息
        return header + json_data
    
    def _parse_binary_response(self, data: bytes) -> dict:
        """
        解析二进制响应消息

        Args:
            data: 二进制响应数据

        Returns:
            解析后的响应数据
        """
        if len(data) < 4:
            print(f"响应数据太短: {len(data)} 字节")
            raise ValueError("响应数据太短")

        # 解析消息头
        header = struct.unpack('>I', data[:4])[0]
        print(f"收到响应头: 0x{header:08X}")

        # 提取各个字段
        protocol_version = (header >> 28) & 0xF
        header_size = (header >> 24) & 0xF
        message_type = (header >> 20) & 0xF
        flags = (header >> 16) & 0xF
        serialization = (header >> 12) & 0xF
        compression = (header >> 8) & 0xF

        print(f"消息类型: {message_type}, 标志: {flags}, 序列化: {serialization}")

        if message_type == 0xB:  # Audio-only server response
            # 音频数据响应
            audio_data = data[4:]
            print(f"收到音频数据: {len(audio_data)} 字节")

            return {
                "type": "audio",
                "sequence": flags,
                "data": audio_data,
                "is_final": flags >= 2
            }
        elif message_type == 0xF:  # Error message
            # 错误消息
            try:
                error_msg = data[4:].decode('utf-8')
                print(f"收到错误消息: {error_msg}")
                return {
                    "type": "error",
                    "message": error_msg
                }
            except UnicodeDecodeError:
                print("解码错误消息失败")
                return {
                    "type": "error",
                    "message": "解码错误消息失败"
                }
        else:
            # 其他类型消息（可能是JSON响应）
            try:
                json_data = data[4:].decode('utf-8')
                print(f"收到JSON数据: {json_data[:200]}...")
                response = json.loads(json_data)
                return {
                    "type": "json",
                    "data": response
                }
            except (UnicodeDecodeError, json.JSONDecodeError) as e:
                print(f"解析JSON失败: {str(e)}")
                return {
                    "type": "unknown",
                    "raw_data": data,
                    "header": header
                }
    
    async def synthesize_stream(
        self,
        text: str,
        voice_type: str = "zh_female_xinlingjitang_moon_bigtts",
        encoding: str = "mp3",
        speed_ratio: float = 1.0,
        rate: int = 24000
    ) -> AsyncGenerator[bytes, None]:
        """
        流式合成语音
        
        Args:
            text: 要合成的文本
            voice_type: 音色类型
            encoding: 音频编码格式
            speed_ratio: 语速
            rate: 音频采样率
            
        Yields:
            音频数据块
        """
        # 生成唯一的请求ID
        reqid = str(uuid.uuid4())
        
        # 构建请求参数
        payload = {
            "app": {
                "appid": self.appid,
                "token": self.token,
                "cluster": self.cluster
            },
            "user": {
                "uid": "user_001"
            },
            "audio": {
                "voice_type": voice_type,
                "encoding": encoding,
                "speed_ratio": speed_ratio,
                "rate": rate
            },
            "request": {
                "reqid": reqid,
                "text": text,
                "operation": "submit"  # 流式合成使用submit
            }
        }
        
        try:
            # 建立WebSocket连接
            async with websockets.connect(
                self.ws_url,
                extra_headers=self.headers,
                ping_interval=None
            ) as websocket:
                
                # 发送请求
                message = self._create_binary_message(payload)
                await websocket.send(message)
                
                print("开始接收音频流...")
                
                # 接收响应
                audio_chunks = []
                while True:
                    try:
                        response_data = await websocket.recv()
                        print(f"收到数据类型: {type(response_data)}, 大小: {len(response_data) if hasattr(response_data, '__len__') else 'N/A'}")

                        if isinstance(response_data, bytes):
                            # 解析二进制响应
                            try:
                                parsed = self._parse_binary_response(response_data)

                                if parsed["type"] == "audio":
                                    audio_data = parsed["data"]
                                    if audio_data:
                                        audio_chunks.append(audio_data)
                                        yield audio_data

                                    # 检查是否为最后一个音频块
                                    if parsed["is_final"]:
                                        print("音频流接收完成")
                                        break

                                elif parsed["type"] == "error":
                                    raise Exception(f"服务器错误: {parsed['message']}")

                                elif parsed["type"] == "json":
                                    # 处理JSON响应
                                    response = parsed["data"]
                                    if response.get("code") != 3000:
                                        raise Exception(f"合成失败: {response.get('message', '未知错误')}")
                                    print(f"收到JSON响应: {response}")

                                else:
                                    print(f"收到未知类型响应: {parsed['type']}")

                            except Exception as e:
                                print(f"解析响应失败: {str(e)}")
                                # 尝试直接作为音频数据处理
                                if len(response_data) > 4:
                                    audio_chunks.append(response_data)
                                    yield response_data

                        elif isinstance(response_data, str):
                            # 文本响应
                            try:
                                response = json.loads(response_data)
                                print(f"收到文本JSON响应: {response}")
                                if response.get("code") != 3000:
                                    raise Exception(f"合成失败: {response.get('message', '未知错误')}")
                            except json.JSONDecodeError:
                                print(f"收到未知文本响应: {response_data}")

                        else:
                            print(f"收到未知数据类型: {type(response_data)}")

                    except websockets.exceptions.ConnectionClosed:
                        print("WebSocket连接已关闭")
                        break
                    except Exception as e:
                        print(f"处理响应时出错: {str(e)}")
                        break

                print(f"总共接收到 {len(audio_chunks)} 个音频块")
                
        except Exception as e:
            raise Exception(f"WebSocket流式合成失败: {str(e)}")
    
    async def synthesize_to_file(
        self,
        text: str,
        output_file: str,
        voice_type: str = "zh_female_xinlingjitang_moon_bigtts",
        encoding: str = "mp3",
        speed_ratio: float = 1.0,
        rate: int = 24000
    ):
        """
        流式合成语音并保存到文件
        
        Args:
            text: 要合成的文本
            output_file: 输出文件路径
            voice_type: 音色类型
            encoding: 音频编码格式
            speed_ratio: 语速
            rate: 音频采样率
        """
        with open(output_file, "wb") as f:
            async for audio_chunk in self.synthesize_stream(
                text, voice_type, encoding, speed_ratio, rate
            ):
                f.write(audio_chunk)
        
        print(f"音频已保存到: {output_file}")


async def main():
    """主函数 - 演示WebSocket流式合成"""
    
    # 配置信息
    appid = "8721493889"
    token = "kShhdfvOpfvUATOML5acIOPfRj2Gq03m"
    cluster = "volcano_tts"
    voice_type = "zh_female_xinlingjitang_moon_bigtts"
    
    # 文本文件路径
    text_file_path = "/Users/<USER>/Downloads/tts/text.txt"
    
    try:
        # 读取文本内容
        if os.path.exists(text_file_path):
            with open(text_file_path, "r", encoding="utf-8") as f:
                text = f.read().strip()
        elif os.path.exists("text.txt"):
            with open("text.txt", "r", encoding="utf-8") as f:
                text = f.read().strip()
        else:
            # 如果文件不存在，使用默认文本
            text = "你好，这是火山语音大模型WebSocket流式合成的测试音频。"
            print("文本文件不存在，使用默认文本")
        
        if not text:
            print("文本内容为空，请检查文件内容")
            return
        
        print(f"要合成的文本: {text}")
        print(f"文本长度: {len(text)} 字符")
        
        # 创建WebSocket TTS客户端
        tts_client = VolcanoTTSWebSocket(appid, token, cluster)
        
        # 生成输出文件名
        output_file = "output_audio_stream.mp3"
        
        # 流式合成语音
        print("开始WebSocket流式合成...")
        await tts_client.synthesize_to_file(
            text=text,
            output_file=output_file,
            voice_type=voice_type,
            encoding="mp3",
            speed_ratio=1.0,
            rate=24000
        )
        
        print("WebSocket流式语音合成完成!")
        
    except Exception as e:
        print(f"错误: {str(e)}")


if __name__ == "__main__":
    asyncio.run(main())
