#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的WebSocket TTS实现
基于官方文档重新实现
"""

import json
import struct
import asyncio
import websockets
import uuid


async def simple_websocket_tts():
    """简化的WebSocket TTS测试"""
    
    # 配置信息
    appid = "8721493889"
    token = "kShhdfvOpfvUATOML5acIOPfRj2Gq03m"
    cluster = "volcano_tts"
    
    ws_url = "wss://openspeech.bytedance.com/api/v1/tts/ws_binary"
    headers = {
        "Authorization": f"Bearer;{token}"
    }
    
    # 测试文本
    text = "你好，这是WebSocket流式合成测试。"
    
    # 构建请求参数
    payload = {
        "app": {
            "appid": appid,
            "token": token,
            "cluster": cluster
        },
        "user": {
            "uid": "test_user"
        },
        "audio": {
            "voice_type": "BV001_streaming",
            "encoding": "mp3",
            "speed_ratio": 1.0,
            "rate": 24000
        },
        "request": {
            "reqid": str(uuid.uuid4()),
            "text": text,
            "operation": "query"
        }
    }
    
    try:
        print("连接WebSocket...")
        async with websockets.connect(
            ws_url,
            extra_headers=headers,
            ping_interval=None
        ) as websocket:
            print("WebSocket连接成功!")
            
            # 创建二进制消息
            json_data = json.dumps(payload).encode('utf-8')
            
            # 根据官方文档构建消息头
            # 0x11101000 = 0001 0001 0001 0000 1000 00000000
            # Protocol version: 0001 (1)
            # Header size: 0001 (1, means 4 bytes)
            # Message type: 0001 (1, full client request)
            # Flags: 0000 (0)
            # Serialization: 0001 (1, JSON)
            # Compression: 0000 (0, no compression)
            # Reserved: 00000000 (0)
            
            header = struct.pack('>I', 0x11101000)
            message = header + json_data
            
            print(f"发送消息，总长度: {len(message)} 字节")
            print(f"JSON数据: {json.dumps(payload, ensure_ascii=False)}")
            
            # 发送消息
            await websocket.send(message)
            print("消息发送成功!")
            
            # 接收响应
            audio_chunks = []
            while True:
                try:
                    response = await asyncio.wait_for(websocket.recv(), timeout=10.0)
                    print(f"收到响应: 类型={type(response)}, 长度={len(response)}")
                    
                    if isinstance(response, bytes):
                        if len(response) >= 4:
                            # 解析消息头
                            header = struct.unpack('>I', response[:4])[0]
                            print(f"响应头: 0x{header:08X}")
                            
                            # 提取消息类型和标志
                            message_type = (header >> 20) & 0xF
                            flags = (header >> 16) & 0xF
                            
                            print(f"消息类型: {message_type}, 标志: {flags}")
                            
                            if message_type == 0xB:  # 音频响应
                                audio_data = response[4:]
                                print(f"收到音频数据: {len(audio_data)} 字节")
                                audio_chunks.append(audio_data)
                                
                                if flags >= 2:  # 最后一个音频块
                                    print("收到最后一个音频块")
                                    break
                            else:
                                # 尝试解析为JSON
                                try:
                                    json_str = response[4:].decode('utf-8')
                                    json_data = json.loads(json_str)
                                    print(f"JSON响应: {json_data}")
                                    
                                    if json_data.get("code") != 3000:
                                        print(f"错误: {json_data.get('message')}")
                                        break
                                except:
                                    print("无法解析响应")
                        else:
                            print("响应数据太短")
                    
                    elif isinstance(response, str):
                        print(f"文本响应: {response}")
                        try:
                            json_data = json.loads(response)
                            print(f"JSON响应: {json_data}")
                        except:
                            pass
                
                except asyncio.TimeoutError:
                    print("接收超时")
                    break
                except websockets.exceptions.ConnectionClosed:
                    print("连接已关闭")
                    break
            
            # 保存音频
            if audio_chunks:
                total_audio = b''.join(audio_chunks)
                with open("simple_websocket_output.mp3", "wb") as f:
                    f.write(total_audio)
                print(f"音频已保存，总大小: {len(total_audio)} 字节")
            else:
                print("没有收到音频数据")
    
    except Exception as e:
        print(f"WebSocket错误: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(simple_websocket_tts())
