#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
火山语音大模型合成API - Web应用
基于Flask的Web界面，支持在线文本转语音
"""

from flask import Flask, render_template, request, jsonify, send_file
import os
import uuid
import time
from pathlib import Path
from volcano_tts_complete import VolcanoTTS
from voice_config import get_all_voices, VOICE_CONFIG, VOICE_CATEGORIES
import logging
from pydub import AudioSegment

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = Flask(__name__)
app.secret_key = 'volcano_tts_secret_key_2024'

# 配置上传和输出目录
UPLOAD_FOLDER = 'uploads'
OUTPUT_FOLDER = 'static/audio'
app.config['UPLOAD_FOLDER'] = UPLOAD_FOLDER
app.config['OUTPUT_FOLDER'] = OUTPUT_FOLDER

# 确保目录存在
Path(UPLOAD_FOLDER).mkdir(exist_ok=True)
Path(OUTPUT_FOLDER).mkdir(exist_ok=True)
Path('static').mkdir(exist_ok=True)

# 创建TTS客户端
tts_client = VolcanoTTS()

@app.route('/')
def index():
    """主页"""
    # 获取支持的音色列表
    voices = get_all_voices()

    # 获取音色分类
    categories = VOICE_CATEGORIES

    # 获取音色详细信息
    voice_details = VOICE_CONFIG

    return render_template(
        'index.html',
        voices=voices,
        categories=categories,
        voice_details=voice_details
    )

@app.route('/synthesize', methods=['POST'])
def synthesize():
    """合成语音API"""
    try:
        # 获取表单数据
        text = request.form.get('text', '').strip()
        voice_type = request.form.get('voice_type', 'zh_female_xinlingjitang_moon_bigtts')
        encoding = request.form.get('encoding', 'mp3')
        speed_ratio = float(request.form.get('speed_ratio', 1.0))
        rate = int(request.form.get('rate', 24000))
        loudness_ratio = float(request.form.get('loudness_ratio', 1.0))
        enable_timestamp = request.form.get('enable_timestamp') == 'on'
        auto_segment = request.form.get('auto_segment') == 'on'

        # 验证文本
        if not text:
            return jsonify({
                'success': False,
                'error': '请输入要合成的文本或上传文本文件'
            })

        text_bytes = len(text.encode('utf-8'))

        # 检查是否需要分段处理
        if text_bytes > 1024 and auto_segment:
            return synthesize_long_text(text, voice_type, encoding, speed_ratio, loudness_ratio, rate, enable_timestamp)
        elif text_bytes > 1024 and not auto_segment:
            return jsonify({
                'success': False,
                'error': f'文本长度超过限制（当前：{text_bytes} 字节，最大：1024 字节）。请启用自动分段处理或手动缩短文本。'
            })
        
        # 生成唯一文件名
        file_id = str(uuid.uuid4())
        output_filename = f"{file_id}.{encoding}"
        output_path = os.path.join(app.config['OUTPUT_FOLDER'], output_filename)
        
        logger.info(f"开始合成语音: 文本长度={len(text)}, 音色={voice_type}, 格式={encoding}")
        
        # 合成语音
        result = tts_client.synthesize_text(
            text=text,
            voice_type=voice_type,
            encoding=encoding,
            speed_ratio=speed_ratio,
            rate=rate,
            loudness_ratio=loudness_ratio,
            output_file=output_path,
            enable_timestamp=enable_timestamp
        )
        
        if result['success']:
            # 返回成功结果
            return jsonify({
                'success': True,
                'audio_url': f'/static/audio/{output_filename}',
                'filename': output_filename,
                'duration_ms': result['duration_ms'],
                'synthesis_time_s': result['synthesis_time_s'],
                'audio_size_bytes': result['audio_size_bytes'],
                'text_length': result['text_length'],
                'voice_type': result['voice_type'],
                'encoding': result['encoding']
            })
        else:
            return jsonify({
                'success': False,
                'error': '语音合成失败'
            })
    
    except ValueError as e:
        logger.error(f"参数错误: {str(e)}")
        return jsonify({
            'success': False,
            'error': f'参数错误: {str(e)}'
        })
    
    except Exception as e:
        logger.error(f"合成失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': f'合成失败: {str(e)}'
        })

@app.route('/upload_text', methods=['POST'])
def upload_text():
    """上传文本文件"""
    try:
        if 'file' not in request.files:
            return jsonify({
                'success': False,
                'error': '没有选择文件'
            })
        
        file = request.files['file']
        if file.filename == '':
            return jsonify({
                'success': False,
                'error': '没有选择文件'
            })
        
        if file and file.filename.lower().endswith('.txt'):
            # 读取文件内容
            content = file.read().decode('utf-8').strip()
            content_bytes = len(content.encode('utf-8'))

            # 不再限制文件大小，支持长文本
            return jsonify({
                'success': True,
                'text': content,
                'length': len(content),
                'bytes': content_bytes,
                'is_long_text': content_bytes > 1024
            })
        else:
            return jsonify({
                'success': False,
                'error': '请上传 .txt 格式的文件'
            })
    
    except Exception as e:
        logger.error(f"文件上传失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': f'文件上传失败: {str(e)}'
        })

@app.route('/download/<filename>')
def download_file(filename):
    """下载音频文件"""
    try:
        file_path = os.path.join(app.config['OUTPUT_FOLDER'], filename)
        if os.path.exists(file_path):
            return send_file(file_path, as_attachment=True)
        else:
            return jsonify({'error': '文件不存在'}), 404
    except Exception as e:
        logger.error(f"文件下载失败: {str(e)}")
        return jsonify({'error': f'下载失败: {str(e)}'}), 500

@app.route('/voices')
def get_voices():
    """获取支持的音色列表API"""
    voices = tts_client.get_voice_list()
    return jsonify(voices)

@app.route('/health')
def health_check():
    """健康检查"""
    return jsonify({
        'status': 'healthy',
        'timestamp': time.time(),
        'service': 'volcano-tts-web'
    })

@app.route('/test')
def test_frontend():
    """前端测试页面"""
    with open('test_frontend.html', 'r', encoding='utf-8') as f:
        return f.read()

@app.errorhandler(404)
def not_found(error):
    """404错误处理"""
    return render_template('404.html'), 404

@app.errorhandler(500)
def internal_error(error):
    """500错误处理"""
    logger.error(f"内部错误: {str(error)}")
    return render_template('500.html'), 500

# 清理旧文件的后台任务
def cleanup_old_files():
    """清理超过1小时的音频文件"""
    try:
        current_time = time.time()
        for filename in os.listdir(app.config['OUTPUT_FOLDER']):
            file_path = os.path.join(app.config['OUTPUT_FOLDER'], filename)
            if os.path.isfile(file_path):
                file_age = current_time - os.path.getctime(file_path)
                if file_age > 3600:  # 1小时
                    os.remove(file_path)
                    logger.info(f"清理旧文件: {filename}")
    except Exception as e:
        logger.error(f"清理文件失败: {str(e)}")

def synthesize_long_text(text, voice_type, encoding, speed_ratio, loudness_ratio, rate, enable_timestamp):
    """长文本分段合成"""
    try:
        logger.info(f"开始长文本分段合成: 文本长度={len(text)}")

        # 分割文本
        segments = tts_client.split_long_text(text)
        logger.info(f"文本分为 {len(segments)} 段")

        audio_urls = []
        audio_files = []  # 存储音频文件路径，用于后续合并
        total_duration = 0

        for i, segment in enumerate(segments):
            # 生成唯一文件名
            file_id = str(uuid.uuid4())
            output_filename = f"{file_id}.{encoding}"
            output_path = os.path.join(app.config['OUTPUT_FOLDER'], output_filename)

            # 合成当前段
            result = tts_client.synthesize_text(
                text=segment,
                voice_type=voice_type,
                encoding=encoding,
                speed_ratio=speed_ratio,
                loudness_ratio=loudness_ratio,
                rate=rate,
                enable_timestamp=enable_timestamp,
                output_file=output_path
            )

            if result['success']:
                audio_urls.append(f'/static/audio/{output_filename}')
                audio_files.append(output_path)
                # 确保duration_ms是数字类型
                duration_ms = result.get('duration_ms', 0)
                if isinstance(duration_ms, str):
                    try:
                        duration_ms = int(duration_ms)
                    except (ValueError, TypeError):
                        duration_ms = 0
                total_duration += duration_ms
                logger.info(f"第 {i+1}/{len(segments)} 段合成完成: {output_filename}")
            else:
                raise Exception(f"第 {i+1} 段合成失败")

        # 合并音频文件
        merged_filename = f"merged_{str(uuid.uuid4())}.{encoding}"
        merged_path = os.path.join(app.config['OUTPUT_FOLDER'], merged_filename)

        # 调用合并函数
        merge_success, merge_message = merge_audio_files(audio_files, merged_path, encoding)

        # 构建响应数据
        response_data = {
            'success': True,
            'audio_urls': audio_urls,
            'segments_count': len(segments),
            'total_duration_ms': total_duration,
            'text_length': len(text),
            'voice_type': voice_type,
            'encoding': encoding,
            'is_long_text': True
        }

        # 如果合并成功，添加合并后的音频URL
        if merge_success:
            response_data['merged_audio_url'] = f'/static/audio/{merged_filename}'
            response_data['merged_audio_filename'] = merged_filename
            logger.info(f"音频合并成功: {merged_filename}")
        else:
            response_data['merge_error'] = merge_message
            logger.warning(f"音频合并失败: {merge_message}")

        logger.info(f"长文本合成完成: 共 {len(segments)} 段")
        return jsonify(response_data)

    except Exception as e:
        logger.error(f"长文本合成失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': f'长文本合成失败: {str(e)}'
        })

def merge_audio_files(file_paths, output_path, format_type='mp3'):
    """
    合并多个音频文件

    Args:
        file_paths: 音频文件路径列表
        output_path: 输出文件路径
        format_type: 音频格式

    Returns:
        (success, message): 成功状态和消息
    """
    try:
        if not file_paths:
            return False, "没有音频文件可合并"

        # 检查所有文件是否存在
        for file_path in file_paths:
            if not os.path.exists(file_path):
                return False, f"文件不存在: {file_path}"

        # 合并音频
        combined = AudioSegment.empty()
        for file_path in file_paths:
            try:
                # 根据文件格式加载音频
                if format_type.lower() == 'mp3':
                    audio = AudioSegment.from_mp3(file_path)
                elif format_type.lower() == 'wav':
                    audio = AudioSegment.from_wav(file_path)
                elif format_type.lower() == 'ogg_opus':
                    audio = AudioSegment.from_ogg(file_path)
                elif format_type.lower() == 'pcm':
                    # PCM需要更多参数，这里使用默认值
                    audio = AudioSegment.from_raw(file_path, sample_width=2, frame_rate=44100, channels=2)
                else:
                    # 默认尝试自动识别
                    audio = AudioSegment.from_file(file_path)

                combined += audio
            except Exception as e:
                logger.error(f"合并音频时出错: {str(e)}")
                return False, f"处理文件 {file_path} 时出错: {str(e)}"

        # 导出合并后的音频
        if format_type.lower() == 'mp3':
            combined.export(output_path, format="mp3")
        elif format_type.lower() == 'wav':
            combined.export(output_path, format="wav")
        elif format_type.lower() == 'ogg_opus':
            combined.export(output_path, format="ogg")
        elif format_type.lower() == 'pcm':
            combined.export(output_path, format="raw")
        else:
            combined.export(output_path, format=format_type.lower())

        return True, "音频合并成功"
    except Exception as e:
        logger.error(f"合并音频失败: {str(e)}")
        return False, f"合并音频失败: {str(e)}"

if __name__ == '__main__':
    # 启动时清理旧文件
    cleanup_old_files()
    
    # 启动Flask应用
    app.run(
        host='0.0.0.0',
        port=3000,
        debug=True,
        threaded=True
    )
