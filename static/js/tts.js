// 火山语音合成 - TTS专用JavaScript

// 全局函数：处理合成按钮点击
function handleSynthesizeClick() {
    console.log('handleSynthesizeClick 被调用');
    synthesizeText();
}

$(document).ready(function() {

    // 初始化
    updateSliderValues();
    updateTextStats();
    initVoiceModal();

    // 滑块值变化监听
    $('#speed_ratio').on('input', function() {
        $('#speed-value').text($(this).val());
    });

    $('#loudness_ratio').on('input', function() {
        $('#volume-value').text($(this).val());
    });

    // 音色选择变化监听
    $('#voice_type').on('change', function() {
        updateVoiceDescription($(this).val());
    });
    
    // 文本输入监听
    $('#text').on('input', function() {
        updateTextStats();
        // 当用户手动输入时，如果当前是文件模式，则切换到文本输入模式
        const currentSource = $(this).data('source');
        if (currentSource === 'file' && $(this).val() !== $(this).data('file-content')) {
            updateInputSource('text', '', $(this).val());
            showAlert('已切换到文本输入模式', 'info');
        }
    });
    
    // 快速文本按钮
    $('.quick-text').on('click', function() {
        const text = $(this).data('text');
        $('#text').val(text);
        updateTextStats();

        // 切换到文本输入模式
        updateInputSource('text', '', text);

        // 添加动画效果
        $('#text').addClass('border-primary');
        setTimeout(() => {
            $('#text').removeClass('border-primary');
        }, 1000);

        showAlert('已填入示例文本', 'info');
    });
    
    // 文件上传处理
    $('#text-file').on('change', function() {
        const file = this.files[0];
        if (file) {
            uploadTextFile(file);
        }
    });
    
    // 拖拽上传
    const textArea = $('#text')[0];
    
    textArea.addEventListener('dragover', function(e) {
        e.preventDefault();
        $(this).addClass('border-primary bg-light');
    });
    
    textArea.addEventListener('dragleave', function(e) {
        e.preventDefault();
        $(this).removeClass('border-primary bg-light');
    });
    
    textArea.addEventListener('drop', function(e) {
        e.preventDefault();
        $(this).removeClass('border-primary bg-light');
        
        const files = e.dataTransfer.files;
        if (files.length > 0) {
            const file = files[0];
            if (file.type === 'text/plain') {
                uploadTextFile(file);
            } else {
                showAlert('请上传文本文件(.txt)', 'warning');
            }
        }
    });
    
    // 表单提交处理
    $('#tts-form').on('submit', function(e) {
        e.preventDefault();
        e.stopPropagation();
        synthesizeText();
        return false;
    });

    // 合成按钮点击处理（备用）
    $('#synthesize-btn').on('click', function(e) {
        e.preventDefault();
        e.stopPropagation();
        synthesizeText();
        return false;
    });

    // 使用事件委托确保事件绑定
    $(document).on('click', '#synthesize-btn', function(e) {
        e.preventDefault();
        e.stopPropagation();
        synthesizeText();
        return false;
    });

    $(document).on('submit', '#tts-form', function(e) {
        e.preventDefault();
        e.stopPropagation();
        synthesizeText();
        return false;
    });
    
    // 重置表单
    $('#reset-form').on('click', function() {
        if (confirm('确定要重置所有设置吗？')) {
            $('#tts-form')[0].reset();
            $('#text').val('');
            $('#speed_ratio').val(1.0);
            $('#loudness_ratio').val(1.0);
            updateSliderValues();
            updateTextStats();
            $('#result-card').hide();
            localStorage.removeItem('tts_form_data');
        }
    });
});

// 上传文本文件
function uploadTextFile(file) {
    const formData = new FormData();
    formData.append('file', file);

    // 显示上传进度
    showAlert('正在读取文件...', 'info');

    $.ajax({
        url: '/upload_text',
        type: 'POST',
        data: formData,
        processData: false,
        contentType: false,
        success: function(response) {
            if (response.success) {
                // 将文件内容填入文本框
                $('#text').val(response.text);
                updateTextStats();

                // 标记当前使用的是文件内容
                $('#text').data('source', 'file');
                $('#text').data('filename', file.name);
                $('#text').data('file-content', response.text); // 保存原始文件内容

                // 清空文件输入框
                $('#text-file').val('');

                // 更新UI状态
                updateInputSource('file', file.name, response.text);

                showAlert(`文件上传成功，共 ${response.length} 个字符。现在可以直接合成或编辑文本。`, 'success');

                // 添加动画效果
                $('#text').addClass('border-success');
                setTimeout(() => {
                    $('#text').removeClass('border-success');
                }, 2000);
            } else {
                showAlert(response.error, 'danger');
            }
        },
        error: function(xhr, status, error) {
            showAlert('文件上传失败: ' + error, 'danger');
        }
    });
}

// 合成语音
function synthesizeText() {
    const text = $('#text').val().trim();

    // 验证文本
    if (!text) {
        showAlert('请输入要合成的文本或上传文本文件', 'warning');
        $('#text').focus();
        return;
    }

    const textBytes = new Blob([text]).size;
    const autoSegment = $('#auto_segment').is(':checked');

    // 检查是否需要分段处理
    if (textBytes > 1024 && autoSegment) {
        synthesizeLongText(text);
        return;
    } else if (textBytes > 1024 && !autoSegment) {
        showAlert('文本长度超过1024字节，请启用自动分段处理或手动缩短文本', 'warning');
        return;
    }

    // 对于短文本，直接合成
    synthesizeSingleText(text);
}

// 显示合成结果
function displayResult(result, clientTime) {
    const resultHtml = `
        <div class="row">
            <div class="col-md-8">
                <h6><i class="fas fa-music me-2"></i>音频播放</h6>
                <audio controls class="audio-player w-100 mb-3">
                    <source src="${result.audio_url}" type="audio/${result.encoding}">
                    您的浏览器不支持音频播放。
                </audio>
                
                <div class="audio-controls">
                    <a href="${result.audio_url}" download="${result.filename}" class="btn btn-success btn-sm">
                        <i class="fas fa-download me-1"></i>下载音频
                    </a>
                    <button type="button" class="btn btn-info btn-sm" onclick="copyToClipboard('${result.audio_url}')">
                        <i class="fas fa-copy me-1"></i>复制链接
                    </button>
                    <button type="button" class="btn btn-secondary btn-sm" onclick="shareAudio('${result.audio_url}')">
                        <i class="fas fa-share me-1"></i>分享
                    </button>
                </div>
            </div>
            
            <div class="col-md-4">
                <h6><i class="fas fa-chart-bar me-2"></i>合成信息</h6>
                <div class="stats-grid">
                    <div class="stat-item">
                        <div class="stat-value">${formatDuration(result.duration_ms)}</div>
                        <div class="stat-label">音频时长</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value">${formatFileSize(result.audio_size_bytes)}</div>
                        <div class="stat-label">文件大小</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value">${result.synthesis_time_s}s</div>
                        <div class="stat-label">服务器耗时</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value">${clientTime.toFixed(2)}s</div>
                        <div class="stat-label">总耗时</div>
                    </div>
                </div>
                
                <div class="mt-3">
                    <small class="text-muted">
                        <strong>参数信息:</strong><br>
                        音色: ${getVoiceName(result.voice_type)}<br>
                        格式: ${result.encoding.toUpperCase()}<br>
                        文本长度: ${result.text_length} 字符
                    </small>
                </div>
            </div>
        </div>
    `;
    
    $('#result-content').html(resultHtml);
    $('#result-card').show().addClass('fade-in');
    
    // 滚动到结果区域
    $('html, body').animate({
        scrollTop: $('#result-card').offset().top - 100
    }, 1000);
    
    // 自动播放音频（如果用户允许）
    setTimeout(() => {
        const audio = $('.audio-player')[0];
        if (audio) {
            audio.play().catch(e => {
                console.log('自动播放被阻止:', e);
            });
        }
    }, 500);
}

// 获取音色名称
function getVoiceName(voiceId) {
    const voiceMap = {
        'zh_female_xinlingjitang_moon_bigtts': '中文女声-心灵鸡汤月亮',
        'zh_male_M392_conversation_wvae_bigtts': '中文男声-对话',
        'BV001_streaming': '基础女声',
        'BV002_streaming': '基础男声'
    };
    return voiceMap[voiceId] || voiceId;
}

// 分享音频
function shareAudio(audioUrl) {
    if (navigator.share) {
        navigator.share({
            title: '火山语音合成结果',
            text: '我使用火山语音合成了一段音频',
            url: audioUrl
        }).catch(err => {
            console.log('分享失败:', err);
            copyToClipboard(audioUrl);
        });
    } else {
        // 降级到复制链接
        copyToClipboard(audioUrl);
    }
}



// 键盘快捷键
$(document).keydown(function(e) {
    // Ctrl+L 清空文本
    if (e.ctrlKey && e.keyCode === 76) {
        e.preventDefault();
        clearInputSource();
    }
});

// 音频播放事件监听
$(document).on('loadedmetadata', '.audio-player', function() {
    console.log('音频加载完成');
});

$(document).on('ended', '.audio-player', function() {
    showAlert('音频播放完成', 'info');
});

// 更新输入源状态
function updateInputSource(source, filename, content) {
    const textArea = $('#text');
    const fileInput = $('#text-file');
    let sourceIndicator = $('#input-source-indicator');

    if (source === 'file') {
        // 文件输入模式
        textArea.data('source', 'file');
        textArea.data('filename', filename);
        textArea.data('file-content', content);

        // 更新UI状态
        textArea.addClass('file-input-mode');
        fileInput.closest('.mb-3').addClass('active-input');

        // 显示文件信息
        if (sourceIndicator.length === 0) {
            const indicator = `
                <div id="input-source-indicator" class="alert alert-info alert-dismissible fade show mt-2" role="alert">
                    <i class="fas fa-file-text me-2"></i>
                    <strong>当前使用文件内容：</strong> <span id="current-filename">${filename}</span>
                    <button type="button" class="btn btn-sm btn-outline-secondary ms-2" onclick="switchToTextInput()">
                        <i class="fas fa-edit me-1"></i>切换到文本输入
                    </button>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" onclick="clearInputSource()"></button>
                </div>
            `;
            textArea.after(indicator);
        } else {
            $('#current-filename').text(filename);
            sourceIndicator.removeClass('d-none').show();
        }

    } else {
        // 文本输入模式
        textArea.data('source', 'text');
        textArea.removeData('filename');
        textArea.removeData('file-content');

        // 更新UI状态
        textArea.removeClass('file-input-mode');
        fileInput.closest('.mb-3').removeClass('active-input');

        // 隐藏文件信息
        if (sourceIndicator.length > 0) {
            sourceIndicator.addClass('d-none').hide();
        }
    }
}

// 切换到文本输入模式
function switchToTextInput() {
    const currentText = $('#text').val();
    updateInputSource('text', '', currentText);
    $('#text').focus();
    showAlert('已切换到文本输入模式，现在可以自由编辑文本', 'info');
}

// 清除输入源
function clearInputSource() {
    $('#text').val('');
    $('#text-file').val('');
    updateInputSource('text', '', '');
    updateTextStats();
    showAlert('已清除所有内容', 'info');
}

// 重新上传文件
function reuploadFile() {
    $('#text-file').click();
}

// 长文本分段合成
function synthesizeLongText(text) {
    const segments = calculateSegments(text);

    if (segments.length <= 1) {
        // 如果不需要分段，直接合成
        synthesizeSingleText(text);
        return;
    }

    // 禁用提交按钮
    const submitBtn = $('#synthesize-btn');
    const originalText = submitBtn.html();
    submitBtn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin me-2"></i>分段合成中...');

    // 显示加载模态框
    $('#loading-modal').modal('show');
    $('#loading-modal .modal-body h5').text(`正在分段合成语音... (共${segments.length}段)`);

    // 记录开始时间
    const startTime = Date.now();

    // 准备表单数据
    const baseFormData = {
        voice_type: $('#voice_type').val(),
        encoding: $('#encoding').val(),
        speed_ratio: $('#speed_ratio').val(),
        loudness_ratio: $('#loudness_ratio').val(),
        rate: $('#rate').val(),
        enable_timestamp: $('#enable_timestamp').is(':checked') ? 'on' : 'off'
    };

    // 分段合成
    synthesizeSegments(segments, baseFormData, startTime, originalText);
}

// 分段合成处理
async function synthesizeSegments(segments, baseFormData, startTime, originalText) {
    const audioUrls = [];
    let totalDuration = 0;

    try {
        for (let i = 0; i < segments.length; i++) {
            const segment = segments[i];
            const formData = { ...baseFormData, text: segment };

            // 更新进度
            $('#loading-modal .modal-body p').text(`正在合成第 ${i + 1} 段，共 ${segments.length} 段...`);

            const response = await $.ajax({
                url: '/synthesize',
                type: 'POST',
                data: formData,
                timeout: 60000
            });

            if (response.success) {
                audioUrls.push(response.audio_url);
                totalDuration += parseInt(response.duration_ms) || 0;
            } else {
                throw new Error(response.error || '合成失败');
            }
        }

        // 合成完成，显示结果
        const endTime = Date.now();
        const clientTime = (endTime - startTime) / 1000;

        const result = {
            success: true,
            audio_urls: audioUrls,
            segments_count: segments.length,
            total_duration_ms: totalDuration,
            synthesis_time_s: clientTime,
            text_length: segments.join('').length,
            voice_type: baseFormData.voice_type,
            encoding: baseFormData.encoding
        };

        displayLongTextResult(result);

    } catch (error) {
        console.error('分段合成失败:', error);
        showAlert('分段合成失败: ' + (error.responseJSON?.error || error.message || '未知错误'), 'danger');
    } finally {
        // 恢复按钮状态
        const submitBtn = $('#synthesize-btn');
        submitBtn.prop('disabled', false).html(originalText);
        $('#loading-modal').modal('hide');
    }
}

// 单段文本合成（原有逻辑）
function synthesizeSingleText(text) {
    // 禁用提交按钮
    const submitBtn = $('#synthesize-btn');
    const originalText = submitBtn.html();
    submitBtn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin me-2"></i>合成中...');

    // 显示加载模态框
    $('#loading-modal').modal('show');

    // 准备表单数据
    const formData = {
        text: text,
        voice_type: $('#voice_type').val(),
        encoding: $('#encoding').val(),
        speed_ratio: $('#speed_ratio').val(),
        loudness_ratio: $('#loudness_ratio').val(),
        rate: $('#rate').val(),
        enable_timestamp: $('#enable_timestamp').is(':checked') ? 'on' : 'off',
        auto_segment: $('#auto_segment').is(':checked') ? 'on' : 'off'
    };

    // 记录开始时间
    const startTime = Date.now();

    // 发送请求
    $.ajax({
        url: '/synthesize',
        type: 'POST',
        data: formData,
        timeout: 60000,
        success: function(response) {
            const endTime = Date.now();
            const clientTime = (endTime - startTime) / 1000;

            if (response.success) {
                if (response.is_long_text) {
                    displayLongTextResult(response);
                } else {
                    displayResult(response, clientTime);
                }
            } else {
                showAlert(response.error, 'danger');
            }
        },
        error: function(xhr, status, error) {
            console.error('合成失败:', error);
            let errorMsg = '合成失败';
            if (xhr.responseJSON && xhr.responseJSON.error) {
                errorMsg += ': ' + xhr.responseJSON.error;
            } else if (status === 'timeout') {
                errorMsg += ': 请求超时，请稍后重试';
            } else {
                errorMsg += ': ' + error;
            }
            showAlert(errorMsg, 'danger');
        },
        complete: function() {
            // 恢复按钮状态
            submitBtn.prop('disabled', false).html(originalText);
            $('#loading-modal').modal('hide');
        }
    });
}

// 显示长文本合成结果
function displayLongTextResult(result) {
    const resultCard = $('#result-card');
    const resultContent = $('#result-content');

    // 如果有合并音频，只显示合并后的完整音频
    if (result.merged_audio_url) {
        const resultHtml = `
            <div class="row">
                <div class="col-md-8">
                    <div class="card border-success mb-4">
                        <div class="card-header bg-success text-white">
                            <h6 class="mb-0">
                                <i class="fas fa-music me-2"></i>合成完成
                            </h6>
                        </div>
                        <div class="card-body">
                            <audio controls class="w-100 audio-player mb-3" autoplay>
                                <source src="${result.merged_audio_url}" type="audio/${result.encoding}">
                                您的浏览器不支持音频播放。
                            </audio>
                            <div class="d-flex gap-2 mb-3">
                                <a href="${result.merged_audio_url}" download="${result.merged_audio_filename || 'merged_audio.' + result.encoding}"
                                   class="btn btn-success">
                                    <i class="fas fa-download me-1"></i>下载音频
                                </a>
                                <button type="button" class="btn btn-outline-success" onclick="copyAudioUrl('${result.merged_audio_url}')">
                                    <i class="fas fa-copy me-1"></i>复制链接
                                </button>
                                <button type="button" class="btn btn-outline-info" onclick="toggleDetailInfo()">
                                    <i class="fas fa-info-circle me-1"></i>详细信息
                                </button>
                            </div>

                            <!-- 详细信息区域，默认隐藏 -->
                            <div id="detail-info" style="display: none;">
                                <hr>
                                <h6><i class="fas fa-info-circle me-2"></i>合成信息</h6>
                                <ul class="list-unstyled small">
                                    <li><strong>处理方式:</strong> 长文本自动分段合成</li>
                                    <li><strong>分段数量:</strong> ${result.segments_count} 段</li>
                                    <li><strong>总时长:</strong> ${(result.total_duration_ms / 1000).toFixed(2)} 秒</li>
                                    <li><strong>文本长度:</strong> ${result.text_length} 字符</li>
                                    <li><strong>音色:</strong> ${getVoiceName(result.voice_type)}</li>
                                    <li><strong>格式:</strong> ${result.encoding.toUpperCase()}</li>
                                    <li><strong>状态:</strong> <span class="text-success">已成功合并</span></li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card bg-light">
                        <div class="card-body">
                            <h6 class="card-title">
                                <i class="fas fa-check-circle text-success me-2"></i>合成成功
                            </h6>
                            <p class="card-text small">
                                • 长文本已自动处理并合并<br>
                                • 生成了一个完整的音频文件<br>
                                • 音质清晰，播放流畅<br>
                                • 可直接下载使用
                            </p>
                            <div class="mt-3">
                                <small class="text-muted">
                                    <i class="fas fa-lightbulb me-1"></i>
                                    提示：点击"详细信息"可查看处理详情
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;

        resultContent.html(resultHtml);
        resultCard.show();

        // 滚动到结果区域
        $('html, body').animate({
            scrollTop: resultCard.offset().top - 100
        }, 1000);

        showAlert(`长文本合成完成！已生成完整音频文件（${(result.total_duration_ms / 1000).toFixed(1)}秒）`, 'success');
    } else {
        // 如果合并失败，显示错误信息和分段音频
        displayLongTextFallback(result);
    }
}

// 初始化音色模态框
function initVoiceModal() {
    // 从服务器获取音色数据（如果有的话），否则使用默认数据
    const voiceData = window.voiceDetails || {
        general: [
            { id: 'zh_female_xinlingjitang_moon_bigtts', name: '心灵鸡汤', gender: '女声', desc: '温暖治愈，适合情感类内容', features: ['温暖', '治愈', '亲切'] },
            { id: 'zh_female_shuangkuaisisi_moon_bigtts', name: '爽快思思', gender: '女声', desc: '爽快直率，中英双语，适合现代化内容', features: ['爽快', '现代', '双语'] },
            { id: 'zh_male_wennuanahu_moon_bigtts', name: '温暖阿虎', gender: '男声', desc: '温和亲切，如邻家大哥般温暖', features: ['温和', '亲切', '稳重'] },
            { id: 'zh_male_shaonianzixin_moon_bigtts', name: '少年梓辛', gender: '男声', desc: '青春活力，充满朝气', features: ['青春', '活力', '朝气'] },
            { id: 'zh_female_linjianvhai_moon_bigtts', name: '邻家女孩', gender: '女声', desc: '清新自然，亲切可人', features: ['清新', '自然', '亲切'] },
            { id: 'zh_male_yuanboxiaoshu_moon_bigtts', name: '渊博小叔', gender: '男声', desc: '知识渊博，适合教育内容', features: ['博学', '稳重', '专业'] },
            { id: 'zh_male_yangguangqingnian_moon_bigtts', name: '阳光青年', gender: '男声', desc: '积极向上，充满正能量', features: ['阳光', '积极', '正能量'] },
            { id: 'zh_female_tianmeixiaoyuan_moon_bigtts', name: '甜美小源', gender: '女声', desc: '甜美可爱，温柔动听', features: ['甜美', '可爱', '温柔'] }
        ],
        roleplay: [
            { id: 'zh_female_gaolengyujie_moon_bigtts', name: '高冷御姐', gender: '女声', desc: '高贵冷艳，气场强大', features: ['高贵', '冷艳', '气场'] },
            { id: 'zh_male_aojiaobazong_moon_bigtts', name: '傲娇霸总', gender: '男声', desc: '霸气十足，傲娇有魅力', features: ['霸气', '傲娇', '魅力'] },
            { id: 'zh_female_meilinvyou_moon_bigtts', name: '魅力女友', gender: '女声', desc: '魅力十足，温柔体贴', features: ['魅力', '温柔', '体贴'] },
            { id: 'zh_male_shenyeboke_moon_bigtts', name: '深夜播客', gender: '男声', desc: '磁性低沉，适合夜间节目', features: ['磁性', '低沉', '深邃'] },
            { id: 'zh_female_sajiaonvyou_moon_bigtts', name: '柔美女友', gender: '女声', desc: '温柔体贴，善于撒娇', features: ['温柔', '体贴', '撒娇'] },
            { id: 'zh_male_dongfanghaoran_moon_bigtts', name: '东方浩然', gender: '男声', desc: '儒雅大气，书卷气息浓厚', features: ['儒雅', '大气', '古风'] }
        ],
        accent: [
            { id: 'zh_male_jingqiangkanye_moon_bigtts', name: '京腔侃爷', gender: '男声', desc: '北京口音浓厚，幽默风趣', features: ['北京口音', '幽默', '风趣'] },
            { id: 'zh_female_wanwanxiaohe_moon_bigtts', name: '湾湾小何', gender: '女声', desc: '台湾口音，甜美温柔', features: ['台湾口音', '甜美', '温柔'] },
            { id: 'zh_female_wanqudashu_moon_bigtts', name: '湾区大叔', gender: '女声', desc: '广东口音，亲切自然', features: ['广东口音', '亲切', '地方特色'] },
            { id: 'zh_female_daimengchuanmei_moon_bigtts', name: '呆萌川妹', gender: '女声', desc: '四川口音，呆萌有趣', features: ['四川口音', '呆萌', '可爱'] },
            { id: 'zh_male_guozhoudege_moon_bigtts', name: '广州德哥', gender: '男声', desc: '广东口音，热情豪爽', features: ['广东口音', '热情', '豪爽'] },
            { id: 'zh_male_beijingxiaoye_moon_bigtts', name: '北京小爷', gender: '男声', desc: '北京小爷音色，京味儿十足', features: ['北京口音', '京味', '传统'] }
        ],
        video: [
            { id: 'zh_male_M100_conversation_wvae_bigtts', name: '悠悠君子', gender: '男声', desc: '稳重大气，适合正式场合', features: ['稳重', '大气', '正式'] },
            { id: 'zh_female_maomao_conversation_wvae_bigtts', name: '文静毛毛', gender: '女声', desc: '文静优雅，适合知性内容', features: ['文静', '优雅', '知性'] },
            { id: 'zh_male_xudong_conversation_wvae_bigtts', name: '快乐小东', gender: '男声', desc: '快乐活泼，充满活力', features: ['快乐', '活泼', '活力'] }
        ],
        multilang: [
            { id: 'en_female_sarah_mars_bigtts', name: 'Sarah', gender: '女声', desc: '澳洲英语，清晰标准', features: ['澳洲口音', '清晰', '标准'] },
            { id: 'en_male_adam_mars_bigtts', name: 'Adam', gender: '男声', desc: '美式英语，自然流畅', features: ['美式口音', '自然', '流畅'] },
            { id: 'en_female_anna_mars_bigtts', name: 'Anna', gender: '女声', desc: '英式英语，优雅动听', features: ['英式口音', '优雅', '动听'] },
            { id: 'en_male_smith_mars_bigtts', name: 'Smith', gender: '男声', desc: '英式英语，绅士风度', features: ['英式口音', '绅士', '正式'] }
        ],
        special: [
            { id: 'zh_female_yingyujiaoyu_mars_bigtts', name: 'Tina老师', gender: '女声', desc: '专业英语教育音色，发音标准清晰', features: ['教育专用', '标准发音', '专业'] },
            { id: 'zh_female_kefunvsheng_mars_bigtts', name: '暖阳女声', gender: '女声', desc: '客服专用音色，温暖亲切', features: ['客服专用', '温暖', '服务感'] },
            { id: 'zh_male_naiqimengwa_mars_bigtts', name: '奶气萌娃', gender: '男声', desc: '奶气十足的萌娃音色，可爱童真', features: ['奶气', '萌娃', '童真'] },
            { id: 'zh_male_sunwukong_mars_bigtts', name: '猴哥', gender: '男声', desc: '孙悟空角色音色，活泼机灵', features: ['经典角色', '活泼', '机灵'] }
        ],
        narration: [
            { id: 'zh_male_jieshuonansheng_mars_bigtts', name: '磁性解说男声', gender: '男声', desc: '磁性十足的解说男声，专业权威', features: ['磁性', '专业', '权威'] },
            { id: 'zh_female_jitangmeimei_mars_bigtts', name: '鸡汤妹妹', gender: '女声', desc: '温暖的鸡汤妹妹音色，治愈人心', features: ['温暖', '治愈', '鸡汤'] },
            { id: 'zh_male_changtianyi_mars_bigtts', name: '悬疑解说', gender: '男声', desc: '悬疑专用解说音色，神秘紧张', features: ['悬疑', '神秘', '紧张'] },
            { id: 'zh_male_ruyaqingnian_mars_bigtts', name: '儒雅青年', gender: '男声', desc: '儒雅的青年解说音色，文质彬彬', features: ['儒雅', '文质彬彬', '知识'] }
        ]
    };

    let selectedVoiceId = $('#voice_type').val();

    // 分类切换事件
    $('#voice-categories .list-group-item').on('click', function() {
        const category = $(this).data('category');
        $('#voice-categories .list-group-item').removeClass('active');
        $(this).addClass('active');
        renderVoiceList(voiceData[category]);
    });

    // 确认选择事件
    $('#confirm-voice-selection').on('click', function() {
        if (selectedVoiceId) {
            $('#voice_type').val(selectedVoiceId);
            updateVoiceDescription(selectedVoiceId);
            $('#voiceModal').modal('hide');
            showAlert('音色选择已更新', 'success');
        }
    });

    // 渲染音色列表
    function renderVoiceList(voices) {
        const voiceList = $('#voice-list');
        let html = '';

        voices.forEach(voice => {
            const isSelected = voice.id === selectedVoiceId ? 'border-primary bg-light' : '';

            // 构建特性标签
            let featureTags = '';
            if (voice.features && voice.features.length > 0) {
                featureTags = voice.features.map(feature =>
                    `<span class="badge bg-light text-dark me-1">${feature}</span>`
                ).join('');
            }

            html += `
                <div class="col-md-6 mb-3">
                    <div class="card voice-card ${isSelected}" data-voice-id="${voice.id}" style="cursor: pointer;">
                        <div class="card-body p-3">
                            <h6 class="card-title mb-1">
                                ${voice.name}
                                <span class="badge bg-${voice.gender === '女声' ? 'pink' : 'blue'} ms-2">${voice.gender}</span>
                            </h6>
                            <p class="card-text small text-muted mb-2">${voice.desc}</p>
                            <div class="feature-tags small">
                                ${featureTags}
                            </div>
                        </div>
                    </div>
                </div>
            `;
        });

        voiceList.html(html);

        // 音色卡片点击事件
        $('.voice-card').on('click', function() {
            selectedVoiceId = $(this).data('voice-id');
            $('.voice-card').removeClass('border-primary bg-light');
            $(this).addClass('border-primary bg-light');
        });
    }

    // 初始化显示通用场景
    renderVoiceList(voiceData.general);

    // 初始化音色描述
    updateVoiceDescription($('#voice_type').val());
}

// 更新音色描述
function updateVoiceDescription(voiceId) {
    // 尝试从全局音色数据中获取描述
    let description = '专业音色 - 高质量语音合成';
    let voiceInfo = null;

    // 从全局音色数据中查找
    if (window.voiceDetails) {
        // 遍历所有分类
        for (const category in window.voiceDetails) {
            if (window.voiceDetails[category] && window.voiceDetails[category][voiceId]) {
                voiceInfo = window.voiceDetails[category][voiceId];
                break;
            }
        }
    }

    // 如果找到了音色信息
    if (voiceInfo) {
        // 构建详细描述
        description = `${voiceInfo.name}（${voiceInfo.gender}）- ${voiceInfo.description}`;

        // 添加适用场景
        if (voiceInfo.scene && voiceInfo.scene.length > 0) {
            description += `\n适用场景：${voiceInfo.scene.join('、')}`;
        }
    } else {
        // 使用备用描述
        const backupDescriptions = {
            'zh_female_xinlingjitang_moon_bigtts': '心灵鸡汤女声 - 温暖治愈，适合情感类内容',
            'zh_female_shuangkuaisisi_moon_bigtts': '爽快思思女声 - 活泼开朗，适合轻松内容',
            'zh_male_wennuanahu_moon_bigtts': '温暖阿虎男声 - 温和亲切，适合叙述类内容',
            'zh_male_shaonianzixin_moon_bigtts': '少年梓辛男声 - 青春活力，适合年轻化内容',
            'zh_female_linjianvhai_moon_bigtts': '邻家女孩女声 - 清新自然，适合日常对话',
            'zh_male_yuanboxiaoshu_moon_bigtts': '渊博小叔男声 - 知识渊博，适合教育内容',
            'zh_male_yangguangqingnian_moon_bigtts': '阳光青年男声 - 积极向上，适合励志内容',
            'zh_female_tianmeixiaoyuan_moon_bigtts': '甜美小源女声 - 甜美可爱，适合温馨内容'
        };

        description = backupDescriptions[voiceId] || description;
    }

    // 更新描述显示
    $('#voice-description').html(description.replace(/\n/g, '<br>'));
}

// 复制音频URL到剪贴板
function copyAudioUrl(url) {
    const fullUrl = window.location.origin + url;
    navigator.clipboard.writeText(fullUrl).then(function() {
        showAlert('音频链接已复制到剪贴板', 'success');
    }).catch(function(err) {
        console.error('复制失败:', err);
        showAlert('复制失败，请手动复制链接', 'warning');
    });
}

// 显示长文本合成失败时的备用方案
function displayLongTextFallback(result) {
    const resultCard = $('#result-card');
    const resultContent = $('#result-content');

    // 构建分段音频列表
    let audioListHtml = '';
    result.audio_urls.forEach((url, index) => {
        audioListHtml += `
            <div class="mb-3">
                <h6>第 ${index + 1} 段音频</h6>
                <audio controls class="w-100 audio-player">
                    <source src="${url}" type="audio/${result.encoding}">
                    您的浏览器不支持音频播放。
                </audio>
                <div class="mt-2">
                    <a href="${url}" download class="btn btn-sm btn-outline-primary">
                        <i class="fas fa-download me-1"></i>下载第 ${index + 1} 段
                    </a>
                </div>
            </div>
        `;
    });

    const resultHtml = `
        <div class="row">
            <div class="col-md-8">
                <div class="alert alert-warning mb-4">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <strong>音频合并失败：</strong>${result.merge_error || '未知错误'}<br>
                    <small>已为您生成分段音频，可按顺序播放</small>
                </div>

                <h6><i class="fas fa-list me-2"></i>分段音频列表</h6>
                ${audioListHtml}

                <div class="mt-4">
                    <h6><i class="fas fa-info-circle me-2"></i>合成信息</h6>
                    <ul class="list-unstyled small">
                        <li><strong>分段数量:</strong> ${result.segments_count} 段</li>
                        <li><strong>总时长:</strong> ${(result.total_duration_ms / 1000).toFixed(2)} 秒</li>
                        <li><strong>文本长度:</strong> ${result.text_length} 字符</li>
                        <li><strong>音色:</strong> ${getVoiceName(result.voice_type)}</li>
                        <li><strong>格式:</strong> ${result.encoding.toUpperCase()}</li>
                    </ul>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card bg-light">
                    <div class="card-body">
                        <h6 class="card-title">
                            <i class="fas fa-info-circle me-2"></i>处理说明
                        </h6>
                        <p class="card-text small">
                            • 文本已分为 ${result.segments_count} 段处理<br>
                            • 每段音频可单独播放和下载<br>
                            • 建议按顺序播放以获得完整体验<br>
                            • 音频合并功能暂时不可用
                        </p>
                    </div>
                </div>
            </div>
        </div>
    `;

    resultContent.html(resultHtml);
    resultCard.show();

    // 滚动到结果区域
    $('html, body').animate({
        scrollTop: resultCard.offset().top - 100
    }, 1000);

    showAlert(`长文本合成完成！共生成 ${result.segments_count} 段音频`, 'warning');
}

// 切换详细信息显示/隐藏
function toggleDetailInfo() {
    const detailInfo = $('#detail-info');
    const button = $('button[onclick="toggleDetailInfo()"]');

    if (detailInfo.is(':visible')) {
        detailInfo.slideUp();
        button.html('<i class="fas fa-info-circle me-1"></i>详细信息');
    } else {
        detailInfo.slideDown();
        button.html('<i class="fas fa-eye-slash me-1"></i>隐藏信息');
    }
}

// 切换分段列表显示/隐藏（保留用于备用方案）
function toggleSegmentList() {
    const segmentList = $('#segment-list');
    const toggleText = $('#toggle-text');

    if (segmentList.is(':visible')) {
        segmentList.hide();
        toggleText.text('显示分段');
    } else {
        segmentList.show();
        toggleText.text('隐藏分段');
    }
}

// 获取音色名称（用于显示）
function getVoiceName(voiceId) {
    // 尝试从全局音色数据中获取名称
    if (window.voiceDetails) {
        for (const category in window.voiceDetails) {
            if (window.voiceDetails[category] && window.voiceDetails[category][voiceId]) {
                return window.voiceDetails[category][voiceId].name;
            }
        }
    }

    // 备用名称映射
    const voiceNames = {
        'zh_female_xinlingjitang_moon_bigtts': '心灵鸡汤',
        'zh_female_shuangkuaisisi_moon_bigtts': '爽快思思',
        'zh_male_wennuanahu_moon_bigtts': '温暖阿虎',
        'zh_male_shaonianzixin_moon_bigtts': '少年梓辛',
        'zh_female_linjianvhai_moon_bigtts': '邻家女孩',
        'zh_male_yuanboxiaoshu_moon_bigtts': '渊博小叔',
        'zh_male_yangguangqingnian_moon_bigtts': '阳光青年',
        'zh_female_tianmeixiaoyuan_moon_bigtts': '甜美小源'
    };

    return voiceNames[voiceId] || voiceId;
}
