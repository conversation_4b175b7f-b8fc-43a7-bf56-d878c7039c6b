// 火山语音合成 - TTS专用JavaScript

$(document).ready(function() {
    // 初始化
    updateSliderValues();
    updateTextStats();
    
    // 滑块值变化监听
    $('#speed_ratio').on('input', function() {
        $('#speed-value').text($(this).val());
    });
    
    $('#loudness_ratio').on('input', function() {
        $('#volume-value').text($(this).val());
    });
    
    // 文本输入监听
    $('#text').on('input', updateTextStats);
    
    // 快速文本按钮
    $('.quick-text').on('click', function() {
        const text = $(this).data('text');
        $('#text').val(text);
        updateTextStats();
        
        // 添加动画效果
        $('#text').addClass('border-primary');
        setTimeout(() => {
            $('#text').removeClass('border-primary');
        }, 1000);
    });
    
    // 文件上传处理
    $('#text-file').on('change', function() {
        const file = this.files[0];
        if (file) {
            uploadTextFile(file);
        }
    });
    
    // 拖拽上传
    const textArea = $('#text')[0];
    
    textArea.addEventListener('dragover', function(e) {
        e.preventDefault();
        $(this).addClass('border-primary bg-light');
    });
    
    textArea.addEventListener('dragleave', function(e) {
        e.preventDefault();
        $(this).removeClass('border-primary bg-light');
    });
    
    textArea.addEventListener('drop', function(e) {
        e.preventDefault();
        $(this).removeClass('border-primary bg-light');
        
        const files = e.dataTransfer.files;
        if (files.length > 0) {
            const file = files[0];
            if (file.type === 'text/plain') {
                uploadTextFile(file);
            } else {
                showAlert('请上传文本文件(.txt)', 'warning');
            }
        }
    });
    
    // 表单提交处理
    $('#tts-form').on('submit', function(e) {
        e.preventDefault();
        synthesizeText();
    });
    
    // 重置表单
    $('#reset-form').on('click', function() {
        if (confirm('确定要重置所有设置吗？')) {
            $('#tts-form')[0].reset();
            $('#text').val('');
            $('#speed_ratio').val(1.0);
            $('#loudness_ratio').val(1.0);
            updateSliderValues();
            updateTextStats();
            $('#result-card').hide();
            localStorage.removeItem('tts_form_data');
        }
    });
});

// 上传文本文件
function uploadTextFile(file) {
    const formData = new FormData();
    formData.append('file', file);
    
    // 显示上传进度
    showAlert('正在读取文件...', 'info');
    
    $.ajax({
        url: '/upload_text',
        type: 'POST',
        data: formData,
        processData: false,
        contentType: false,
        success: function(response) {
            if (response.success) {
                $('#text').val(response.text);
                updateTextStats();
                showAlert(`文件上传成功，共 ${response.length} 个字符`, 'success');
                
                // 添加动画效果
                $('#text').addClass('border-success');
                setTimeout(() => {
                    $('#text').removeClass('border-success');
                }, 2000);
            } else {
                showAlert(response.error, 'danger');
            }
        },
        error: function(xhr, status, error) {
            showAlert('文件上传失败: ' + error, 'danger');
        }
    });
}

// 合成语音
function synthesizeText() {
    const text = $('#text').val().trim();
    
    // 验证文本
    if (!text) {
        showAlert('请输入要合成的文本', 'warning');
        $('#text').focus();
        return;
    }
    
    if (new Blob([text]).size > 1024) {
        showAlert('文本长度超过1024字节限制', 'danger');
        return;
    }
    
    // 禁用提交按钮
    const submitBtn = $('#synthesize-btn');
    const originalText = submitBtn.html();
    submitBtn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin me-2"></i>合成中...');
    
    // 显示加载模态框
    $('#loading-modal').modal('show');
    
    // 准备表单数据
    const formData = {
        text: text,
        voice_type: $('#voice_type').val(),
        encoding: $('#encoding').val(),
        speed_ratio: $('#speed_ratio').val(),
        loudness_ratio: $('#loudness_ratio').val(),
        rate: $('#rate').val(),
        enable_timestamp: $('#enable_timestamp').is(':checked') ? 'on' : 'off'
    };
    
    // 记录开始时间
    const startTime = Date.now();
    
    // 发送请求
    $.ajax({
        url: '/synthesize',
        type: 'POST',
        data: formData,
        timeout: 60000, // 60秒超时
        success: function(response) {
            const endTime = Date.now();
            const clientTime = (endTime - startTime) / 1000;
            
            if (response.success) {
                displayResult(response, clientTime);
                showAlert('语音合成成功！', 'success');
            } else {
                showAlert(response.error, 'danger');
            }
        },
        error: function(xhr, status, error) {
            let errorMessage = '合成失败';
            
            if (status === 'timeout') {
                errorMessage = '请求超时，请重试';
            } else if (xhr.responseJSON && xhr.responseJSON.error) {
                errorMessage = xhr.responseJSON.error;
            } else {
                errorMessage = `网络错误: ${error}`;
            }
            
            showAlert(errorMessage, 'danger');
        },
        complete: function() {
            // 恢复提交按钮
            submitBtn.prop('disabled', false).html(originalText);
            
            // 隐藏加载模态框
            $('#loading-modal').modal('hide');
        }
    });
}

// 显示合成结果
function displayResult(result, clientTime) {
    const resultHtml = `
        <div class="row">
            <div class="col-md-8">
                <h6><i class="fas fa-music me-2"></i>音频播放</h6>
                <audio controls class="audio-player w-100 mb-3">
                    <source src="${result.audio_url}" type="audio/${result.encoding}">
                    您的浏览器不支持音频播放。
                </audio>
                
                <div class="audio-controls">
                    <a href="${result.audio_url}" download="${result.filename}" class="btn btn-success btn-sm">
                        <i class="fas fa-download me-1"></i>下载音频
                    </a>
                    <button type="button" class="btn btn-info btn-sm" onclick="copyToClipboard('${result.audio_url}')">
                        <i class="fas fa-copy me-1"></i>复制链接
                    </button>
                    <button type="button" class="btn btn-secondary btn-sm" onclick="shareAudio('${result.audio_url}')">
                        <i class="fas fa-share me-1"></i>分享
                    </button>
                </div>
            </div>
            
            <div class="col-md-4">
                <h6><i class="fas fa-chart-bar me-2"></i>合成信息</h6>
                <div class="stats-grid">
                    <div class="stat-item">
                        <div class="stat-value">${formatDuration(result.duration_ms)}</div>
                        <div class="stat-label">音频时长</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value">${formatFileSize(result.audio_size_bytes)}</div>
                        <div class="stat-label">文件大小</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value">${result.synthesis_time_s}s</div>
                        <div class="stat-label">服务器耗时</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value">${clientTime.toFixed(2)}s</div>
                        <div class="stat-label">总耗时</div>
                    </div>
                </div>
                
                <div class="mt-3">
                    <small class="text-muted">
                        <strong>参数信息:</strong><br>
                        音色: ${getVoiceName(result.voice_type)}<br>
                        格式: ${result.encoding.toUpperCase()}<br>
                        文本长度: ${result.text_length} 字符
                    </small>
                </div>
            </div>
        </div>
    `;
    
    $('#result-content').html(resultHtml);
    $('#result-card').show().addClass('fade-in');
    
    // 滚动到结果区域
    $('html, body').animate({
        scrollTop: $('#result-card').offset().top - 100
    }, 1000);
    
    // 自动播放音频（如果用户允许）
    setTimeout(() => {
        const audio = $('.audio-player')[0];
        if (audio) {
            audio.play().catch(e => {
                console.log('自动播放被阻止:', e);
            });
        }
    }, 500);
}

// 获取音色名称
function getVoiceName(voiceId) {
    const voiceMap = {
        'zh_female_xinlingjitang_moon_bigtts': '中文女声-心灵鸡汤月亮',
        'zh_male_M392_conversation_wvae_bigtts': '中文男声-对话',
        'BV001_streaming': '基础女声',
        'BV002_streaming': '基础男声'
    };
    return voiceMap[voiceId] || voiceId;
}

// 分享音频
function shareAudio(audioUrl) {
    if (navigator.share) {
        navigator.share({
            title: '火山语音合成结果',
            text: '我使用火山语音合成了一段音频',
            url: audioUrl
        }).catch(err => {
            console.log('分享失败:', err);
            copyToClipboard(audioUrl);
        });
    } else {
        // 降级到复制链接
        copyToClipboard(audioUrl);
    }
}

// 预设文本管理
const presetTexts = [
    "你好，欢迎使用火山语音大模型合成API。",
    "今天天气很好，适合出门散步。希望大家都有美好的一天。",
    "人工智能技术正在快速发展，语音合成技术也越来越成熟。",
    "感谢您的使用，如有问题请联系技术支持。",
    "这是一个测试文本，用于验证语音合成功能是否正常工作。"
];

// 随机获取预设文本
function getRandomPresetText() {
    const randomIndex = Math.floor(Math.random() * presetTexts.length);
    return presetTexts[randomIndex];
}

// 添加随机文本按钮功能
$(document).on('click', '#random-text-btn', function() {
    const randomText = getRandomPresetText();
    $('#text').val(randomText);
    updateTextStats();
    showAlert('已填入随机文本', 'info');
});

// 键盘快捷键
$(document).keydown(function(e) {
    // Ctrl+R 随机文本
    if (e.ctrlKey && e.keyCode === 82) {
        e.preventDefault();
        $('#random-text-btn').click();
    }
    
    // Ctrl+L 清空文本
    if (e.ctrlKey && e.keyCode === 76) {
        e.preventDefault();
        $('#text').val('');
        updateTextStats();
    }
});

// 音频播放事件监听
$(document).on('loadedmetadata', '.audio-player', function() {
    console.log('音频加载完成');
});

$(document).on('ended', '.audio-player', function() {
    showAlert('音频播放完成', 'info');
});
