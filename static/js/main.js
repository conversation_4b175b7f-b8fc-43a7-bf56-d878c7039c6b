// 火山语音合成 - 主要JavaScript文件

$(document).ready(function() {
    // 更新当前时间
    function updateTime() {
        const now = new Date();
        const timeString = now.toLocaleString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
        });
        $('#current-time').text(timeString);
    }
    
    // 每秒更新时间
    updateTime();
    setInterval(updateTime, 1000);
    
    // 初始化工具提示
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
    
    // 平滑滚动
    $('a[href^="#"]').on('click', function(event) {
        var target = $(this.getAttribute('href'));
        if( target.length ) {
            event.preventDefault();
            $('html, body').stop().animate({
                scrollTop: target.offset().top - 100
            }, 1000);
        }
    });
    
    // 添加页面加载动画
    $('body').addClass('fade-in');
    
    // 卡片悬停效果
    $('.card').hover(
        function() {
            $(this).addClass('shadow-lg');
        },
        function() {
            $(this).removeClass('shadow-lg');
        }
    );
    
    // 表单验证增强（排除TTS表单，因为它有自定义处理）
    $('form:not(#tts-form)').on('submit', function(e) {
        var form = this;
        if (!form.checkValidity()) {
            e.preventDefault();
            e.stopPropagation();
        }
        $(form).addClass('was-validated');
    });
    
    // 自动保存表单数据到localStorage
    function saveFormData() {
        const formData = {
            text: $('#text').val(),
            voice_type: $('#voice_type').val(),
            encoding: $('#encoding').val(),
            speed_ratio: $('#speed_ratio').val(),
            loudness_ratio: $('#loudness_ratio').val(),
            rate: $('#rate').val(),
            enable_timestamp: $('#enable_timestamp').is(':checked')
        };
        localStorage.setItem('tts_form_data', JSON.stringify(formData));
    }
    
    // 从localStorage恢复表单数据
    function loadFormData() {
        const savedData = localStorage.getItem('tts_form_data');
        if (savedData) {
            try {
                const formData = JSON.parse(savedData);
                $('#text').val(formData.text || '');
                $('#voice_type').val(formData.voice_type || 'zh_female_xinlingjitang_moon_bigtts');
                $('#encoding').val(formData.encoding || 'mp3');
                $('#speed_ratio').val(formData.speed_ratio || 1.0);
                $('#loudness_ratio').val(formData.loudness_ratio || 1.0);
                $('#rate').val(formData.rate || 24000);
                $('#enable_timestamp').prop('checked', formData.enable_timestamp || false);
                
                // 更新显示值
                updateSliderValues();
                updateTextStats();
            } catch (e) {
                console.log('无法恢复表单数据:', e);
            }
        }
    }
    
    // 监听表单变化并自动保存
    $('#tts-form input, #tts-form select, #tts-form textarea').on('change input', function() {
        saveFormData();
    });
    
    // 页面加载时恢复表单数据
    loadFormData();
    
    // 键盘快捷键
    $(document).keydown(function(e) {
        // Ctrl+Enter 提交表单
        if (e.ctrlKey && e.keyCode === 13) {
            e.preventDefault();
            $('#tts-form').submit();
        }
        
        // Esc 键关闭模态框
        if (e.keyCode === 27) {
            $('.modal').modal('hide');
        }
    });
    
    // 添加页面离开确认（如果有未保存的更改）
    let hasUnsavedChanges = false;
    
    $('#tts-form input, #tts-form select, #tts-form textarea').on('change input', function() {
        hasUnsavedChanges = true;
    });
    
    $('#tts-form').on('submit', function() {
        hasUnsavedChanges = false;
    });
    
    $(window).on('beforeunload', function(e) {
        if (hasUnsavedChanges) {
            const message = '您有未保存的更改，确定要离开吗？';
            e.returnValue = message;
            return message;
        }
    });
    
    // 错误处理
    window.onerror = function(msg, url, lineNo, columnNo, error) {
        console.error('JavaScript错误:', {
            message: msg,
            source: url,
            line: lineNo,
            column: columnNo,
            error: error
        });
        
        // 显示用户友好的错误消息
        showAlert('发生了一个错误，请刷新页面重试。', 'danger');
        
        return false;
    };
    
    // 显示警告消息的辅助函数
    window.showAlert = function(message, type = 'info') {
        const alertHtml = `
            <div class="alert alert-${type} alert-dismissible fade show" role="alert">
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `;
        
        // 在页面顶部插入警告
        $('main .container').prepend(alertHtml);
        
        // 5秒后自动关闭
        setTimeout(function() {
            $('.alert').fadeOut();
        }, 5000);
    };
    
    // 网络状态检测
    function checkNetworkStatus() {
        if (!navigator.onLine) {
            showAlert('网络连接已断开，请检查您的网络连接。', 'warning');
        }
    }
    
    window.addEventListener('online', function() {
        showAlert('网络连接已恢复。', 'success');
    });
    
    window.addEventListener('offline', function() {
        showAlert('网络连接已断开。', 'warning');
    });
    
    // 初始检查网络状态
    checkNetworkStatus();
});

// 更新滑块值显示
function updateSliderValues() {
    $('#speed-value').text($('#speed_ratio').val());
    $('#volume-value').text($('#loudness_ratio').val());
}

// 更新文本统计信息
function updateTextStats() {
    const text = $('#text').val();
    const charCount = text.length;
    const byteCount = new Blob([text]).size;

    $('#text-length').text(charCount);
    $('#text-bytes').text(byteCount);

    // 计算分段信息
    const segments = calculateSegments(text);
    const segmentInfo = $('#segment-info');

    if (segments.length > 1) {
        segmentInfo.html(`<i class="fas fa-cut me-1"></i>将分为 ${segments.length} 段处理`);
        segmentInfo.removeClass('d-none');
    } else {
        segmentInfo.addClass('d-none');
    }

    // 根据字节数改变颜色
    const bytesElement = $('#text-bytes');
    if (byteCount > 1024) {
        bytesElement.removeClass('text-success text-warning').addClass('text-info');
    } else if (byteCount > 800) {
        bytesElement.removeClass('text-success text-danger text-info').addClass('text-warning');
    } else {
        bytesElement.removeClass('text-warning text-danger text-info').addClass('text-success');
    }
}

// 计算文本分段
function calculateSegments(text) {
    const maxBytes = 1024;
    const segments = [];

    if (new Blob([text]).size <= maxBytes) {
        return [text];
    }

    // 按句子分割
    const sentences = text.split(/([。！？；.!?;])/);
    let currentSegment = '';

    for (let i = 0; i < sentences.length; i++) {
        const sentence = sentences[i];
        const testSegment = currentSegment + sentence;

        if (new Blob([testSegment]).size > maxBytes && currentSegment) {
            segments.push(currentSegment.trim());
            currentSegment = sentence;
        } else {
            currentSegment = testSegment;
        }
    }

    if (currentSegment.trim()) {
        segments.push(currentSegment.trim());
    }

    return segments.filter(seg => seg.length > 0);
}

// 格式化文件大小
function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// 格式化时间
function formatDuration(ms) {
    const seconds = Math.floor(ms / 1000);
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    
    if (minutes > 0) {
        return `${minutes}分${remainingSeconds}秒`;
    } else {
        return `${remainingSeconds}秒`;
    }
}

// 复制到剪贴板
function copyToClipboard(text) {
    if (navigator.clipboard && window.isSecureContext) {
        navigator.clipboard.writeText(text).then(function() {
            showAlert('已复制到剪贴板', 'success');
        }).catch(function(err) {
            console.error('复制失败:', err);
            fallbackCopyTextToClipboard(text);
        });
    } else {
        fallbackCopyTextToClipboard(text);
    }
}

// 降级复制方案
function fallbackCopyTextToClipboard(text) {
    const textArea = document.createElement('textarea');
    textArea.value = text;
    textArea.style.position = 'fixed';
    textArea.style.left = '-999999px';
    textArea.style.top = '-999999px';
    document.body.appendChild(textArea);
    textArea.focus();
    textArea.select();

    try {
        const successful = document.execCommand('copy');
        if (successful) {
            showAlert('已复制到剪贴板', 'success');
        } else {
            showAlert('复制失败，请手动复制', 'warning');
        }
    } catch (err) {
        console.error('复制失败:', err);
        showAlert('复制失败，请手动复制', 'warning');
    }

    document.body.removeChild(textArea);
}
