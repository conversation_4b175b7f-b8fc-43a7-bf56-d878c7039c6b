/* 火山语音合成 - 自定义样式 */

/* 全局样式 */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f8f9fa;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

main {
    flex: 1;
}

/* 导航栏样式 */
.navbar-brand {
    font-weight: bold;
    font-size: 1.3rem;
}

.navbar-brand i {
    color: #ffc107;
}

/* 卡片样式 */
.card {
    border: none;
    border-radius: 12px;
    transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.1) !important;
}

.card-header {
    border-radius: 12px 12px 0 0 !important;
    border: none;
    font-weight: 600;
}

/* 表单样式 */
.form-control, .form-select {
    border-radius: 8px;
    border: 2px solid #e9ecef;
    transition: border-color 0.3s ease, box-shadow 0.3s ease;
}

.form-control:focus, .form-select:focus {
    border-color: #0d6efd;
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

/* 文本区域样式 */
#text {
    resize: vertical;
    min-height: 120px;
}

/* 范围滑块样式 */
.form-range {
    height: 6px;
}

.form-range::-webkit-slider-thumb {
    width: 20px;
    height: 20px;
    background: #0d6efd;
    border-radius: 50%;
    border: 2px solid #fff;
    box-shadow: 0 2px 6px rgba(0,0,0,0.2);
}

.form-range::-moz-range-thumb {
    width: 20px;
    height: 20px;
    background: #0d6efd;
    border-radius: 50%;
    border: 2px solid #fff;
    box-shadow: 0 2px 6px rgba(0,0,0,0.2);
}

/* 按钮样式 */
.btn {
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-primary {
    background: linear-gradient(135deg, #0d6efd 0%, #0056b3 100%);
    border: none;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #0056b3 0%, #004085 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(13, 110, 253, 0.3);
}

.btn-success {
    background: linear-gradient(135deg, #198754 0%, #146c43 100%);
    border: none;
}

.btn-info {
    background: linear-gradient(135deg, #0dcaf0 0%, #0aa2c0 100%);
    border: none;
}

/* 快速文本按钮 */
.quick-text {
    margin: 2px;
    font-size: 0.85rem;
}

/* 徽章样式 */
.badge {
    font-size: 0.75rem;
    padding: 0.35em 0.65em;
}

/* 音频播放器样式 */
.audio-player {
    width: 100%;
    margin: 15px 0;
}

.audio-controls {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-top: 10px;
}

/* 结果卡片样式 */
#result-card {
    animation: slideInUp 0.5s ease-out;
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 统计信息样式 */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin: 20px 0;
}

.stat-item {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 8px;
    text-align: center;
    border-left: 4px solid #0d6efd;
}

.stat-value {
    font-size: 1.5rem;
    font-weight: bold;
    color: #0d6efd;
}

.stat-label {
    font-size: 0.9rem;
    color: #6c757d;
    margin-top: 5px;
}

/* 进度条样式 */
.progress {
    height: 8px;
    border-radius: 4px;
    background-color: #e9ecef;
}

.progress-bar {
    border-radius: 4px;
}

/* 加载动画 */
.spinner-border {
    width: 3rem;
    height: 3rem;
}

/* 文件上传区域 */
.file-upload-area {
    border: 2px dashed #dee2e6;
    border-radius: 8px;
    padding: 20px;
    text-align: center;
    transition: border-color 0.3s ease;
    cursor: pointer;
}

.file-upload-area:hover {
    border-color: #0d6efd;
    background-color: #f8f9fa;
}

.file-upload-area.dragover {
    border-color: #0d6efd;
    background-color: #e7f3ff;
}

/* 输入模式样式 */
.file-input-mode {
    border-left: 4px solid #28a745 !important;
    background-color: #f8fff9;
}

.active-input {
    background-color: #e7f3ff;
    border-radius: 8px;
    padding: 10px;
    border: 1px solid #0d6efd;
}

/* 输入源指示器 */
#input-source-indicator {
    border-left: 4px solid #17a2b8;
    background-color: #d1ecf1;
    border-color: #bee5eb;
}

#input-source-indicator .btn {
    font-size: 0.8rem;
    padding: 0.25rem 0.5rem;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container {
        padding: 0 15px;
    }
    
    .card-body {
        padding: 1rem;
    }
    
    .btn-group-vertical .btn {
        margin-bottom: 5px;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
    }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
    body {
        background-color: #212529;
        color: #fff;
    }
    
    .card {
        background-color: #343a40;
        color: #fff;
    }
    
    .form-control, .form-select {
        background-color: #495057;
        border-color: #6c757d;
        color: #fff;
    }
    
    .form-control:focus, .form-select:focus {
        background-color: #495057;
        border-color: #0d6efd;
        color: #fff;
    }
}

/* 动画效果 */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.slide-in {
    animation: slideIn 0.5s ease-out;
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateX(-20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* 工具提示样式 */
.tooltip {
    font-size: 0.875rem;
}

/* 自定义滚动条 */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* 音频合并功能样式 */
.audio-player {
    border-radius: 8px;
    background-color: #f8f9fa;
    margin-bottom: 10px;
}

/* 合并音频卡片样式 */
.card.border-success {
    box-shadow: 0 4px 6px rgba(40, 167, 69, 0.1);
    transition: all 0.3s ease;
}

.card.border-success:hover {
    box-shadow: 0 6px 12px rgba(40, 167, 69, 0.15);
}

/* 分段列表动画 */
#segment-list {
    transition: all 0.3s ease;
}

/* 音色卡片增强样式 */
.voice-card {
    transition: all 0.2s ease;
    cursor: pointer;
}

.voice-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

/* 特征标签样式 */
.feature-tags .badge {
    font-size: 0.7em;
    margin-bottom: 2px;
}

/* 音色选择模态框样式 */
.modal-lg {
    max-width: 900px;
}

/* 音色分类按钮样式 */
.list-group-item-action:hover {
    background-color: #f8f9fa;
    transform: translateX(5px);
    transition: all 0.2s ease;
}

/* 合并音频成功提示样式 */
.alert-success {
    border-left: 4px solid #28a745;
}

/* 音频控制按钮样式 */
.btn-success {
    background: linear-gradient(45deg, #28a745, #20c997);
    border: none;
    transition: all 0.3s ease;
}

.btn-success:hover {
    background: linear-gradient(45deg, #218838, #1ea080);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(40, 167, 69, 0.3);
}
