#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
WebSocket调试脚本
"""

import json
import struct
import asyncio
import websockets


async def test_websocket_connection():
    """测试WebSocket连接"""
    
    # 配置信息
    appid = "8721493889"
    token = "kShhdfvOpfvUATOML5acIOPfRj2Gq03m"
    cluster = "volcano_tts"
    
    ws_url = "wss://openspeech.bytedance.com/api/v1/tts/ws_binary"
    headers = {
        "Authorization": f"Bearer;{token}"
    }
    
    # 简单的测试文本
    text = "你好，这是WebSocket测试。"
    
    # 构建请求参数
    payload = {
        "app": {
            "appid": appid,
            "token": token,
            "cluster": cluster
        },
        "user": {
            "uid": "test_user"
        },
        "audio": {
            "voice_type": "zh_female_xinlingjitang_moon_bigtts",
            "encoding": "mp3",
            "speed_ratio": 1.0,
            "rate": 24000
        },
        "request": {
            "reqid": "test_request_001",
            "text": text,
            "operation": "submit"
        }
    }
    
    try:
        print("尝试连接WebSocket...")
        print(f"URL: {ws_url}")
        print(f"Headers: {headers}")
        
        async with websockets.connect(
            ws_url,
            extra_headers=headers,
            ping_interval=None
        ) as websocket:
            print("WebSocket连接成功!")
            
            # 创建二进制消息
            json_data = json.dumps(payload).encode('utf-8')
            
            # 构建消息头
            protocol_version = 1  # 4 bits
            header_size = 1       # 4 bits
            message_type = 1      # 4 bits
            flags = 0            # 4 bits
            serialization = 1    # 4 bits
            compression = 0      # 4 bits
            reserved = 0         # 8 bits
            
            header_value = (
                (protocol_version << 28) |
                (header_size << 24) |
                (message_type << 20) |
                (flags << 16) |
                (serialization << 12) |
                (compression << 8) |
                reserved
            )
            
            header = struct.pack('>I', header_value)
            message = header + json_data
            
            print(f"发送消息头: 0x{header_value:08X}")
            print(f"JSON数据: {json.dumps(payload, indent=2, ensure_ascii=False)}")
            print(f"消息总长度: {len(message)} 字节")
            
            # 发送消息
            await websocket.send(message)
            print("消息发送成功!")
            
            # 等待响应
            print("等待响应...")
            timeout_count = 0
            max_timeout = 10  # 最多等待10次
            
            while timeout_count < max_timeout:
                try:
                    response = await asyncio.wait_for(websocket.recv(), timeout=2.0)
                    print(f"收到响应: 类型={type(response)}, 长度={len(response) if hasattr(response, '__len__') else 'N/A'}")
                    
                    if isinstance(response, bytes):
                        print(f"二进制数据前16字节: {response[:16].hex()}")
                        
                        if len(response) >= 4:
                            header = struct.unpack('>I', response[:4])[0]
                            print(f"响应头: 0x{header:08X}")
                            
                            message_type = (header >> 20) & 0xF
                            flags = (header >> 16) & 0xF
                            
                            print(f"消息类型: {message_type}, 标志: {flags}")
                            
                            if message_type == 0xB:  # 音频响应
                                audio_data = response[4:]
                                print(f"音频数据长度: {len(audio_data)} 字节")
                                if flags >= 2:
                                    print("这是最后一个音频块")
                                    break
                            else:
                                # 尝试解析为JSON
                                try:
                                    json_str = response[4:].decode('utf-8')
                                    json_data = json.loads(json_str)
                                    print(f"JSON响应: {json_data}")
                                except:
                                    print("无法解析为JSON")
                    
                    elif isinstance(response, str):
                        print(f"文本响应: {response}")
                        try:
                            json_data = json.loads(response)
                            print(f"JSON响应: {json_data}")
                        except:
                            print("无法解析为JSON")
                    
                    timeout_count = 0  # 重置超时计数
                    
                except asyncio.TimeoutError:
                    timeout_count += 1
                    print(f"等待超时 ({timeout_count}/{max_timeout})")
                    if timeout_count >= max_timeout:
                        print("达到最大等待时间，退出")
                        break
                except websockets.exceptions.ConnectionClosed:
                    print("连接已关闭")
                    break
            
            print("测试完成")
    
    except Exception as e:
        print(f"WebSocket测试失败: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(test_websocket_connection())
