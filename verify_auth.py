#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证认证信息
"""

import json
import requests
import uuid


def test_http_auth():
    """测试HTTP接口认证"""
    
    # 配置信息
    appid = "8721493889"
    token = "kShhdfvOpfvUATOML5acIOPfRj2Gq03m"
    cluster = "volcano_tts"
    
    url = "https://openspeech.bytedance.com/api/v1/tts"
    headers = {
        "Authorization": f"Bearer;{token}",
        "Content-Type": "application/json"
    }
    
    # 简单的测试请求
    payload = {
        "app": {
            "appid": appid,
            "token": token,
            "cluster": cluster
        },
        "user": {
            "uid": "test_user"
        },
        "audio": {
            "voice_type": "zh_female_xinlingjitang_moon_bigtts",
            "encoding": "mp3",
            "speed_ratio": 1.0,
            "rate": 24000
        },
        "request": {
            "reqid": str(uuid.uuid4()),
            "text": "认证测试",
            "operation": "query"
        }
    }
    
    try:
        print("测试HTTP认证...")
        print(f"URL: {url}")
        print(f"Headers: {headers}")
        print(f"Payload: {json.dumps(payload, indent=2, ensure_ascii=False)}")
        
        response = requests.post(
            url,
            headers=headers,
            data=json.dumps(payload),
            timeout=10
        )
        
        print(f"HTTP状态码: {response.status_code}")
        print(f"响应头: {dict(response.headers)}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"响应内容: {json.dumps(result, indent=2, ensure_ascii=False)}")
            
            if result.get("code") == 3000:
                print("✅ HTTP认证成功!")
                return True
            else:
                print(f"❌ 业务错误: {result.get('message', '未知错误')}")
                return False
        else:
            print(f"❌ HTTP请求失败: {response.text}")
            return False
    
    except Exception as e:
        print(f"❌ 请求异常: {str(e)}")
        return False


if __name__ == "__main__":
    test_http_auth()
