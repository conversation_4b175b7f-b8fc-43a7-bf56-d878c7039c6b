#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
火山语音大模型合成API - 使用示例
演示各种使用场景
"""

from volcano_tts_complete import VolcanoTTS, VolcanoTTSConfig
import os


def example_basic_usage():
    """基础使用示例"""
    print("=" * 50)
    print("基础使用示例")
    print("=" * 50)
    
    # 创建TTS客户端
    tts = VolcanoTTS()
    
    # 简单的文本合成
    text = "你好，欢迎使用火山语音大模型合成API。"
    result = tts.synthesize_text(
        text=text,
        output_file="example_basic.mp3"
    )
    
    print(f"合成结果: {result['success']}")
    print(f"音频文件: {result.get('output_file')}")
    print(f"音频时长: {result['duration_ms']}ms")


def example_custom_voice():
    """自定义音色示例"""
    print("=" * 50)
    print("自定义音色示例")
    print("=" * 50)
    
    tts = VolcanoTTS()
    
    # 使用不同的音色
    text = "这是使用男声音色的测试。"
    result = tts.synthesize_text(
        text=text,
        voice_type="zh_male_M392_conversation_wvae_bigtts",
        output_file="example_male_voice.mp3"
    )
    
    print(f"使用音色: {result['voice_type']}")
    print(f"音频文件: {result.get('output_file')}")


def example_custom_parameters():
    """自定义参数示例"""
    print("=" * 50)
    print("自定义参数示例")
    print("=" * 50)
    
    tts = VolcanoTTS()
    
    # 自定义语速、音量等参数
    text = "这是一个语速较快、音量较大的测试。"
    result = tts.synthesize_text(
        text=text,
        speed_ratio=1.5,  # 1.5倍语速
        loudness_ratio=1.5,  # 1.5倍音量
        encoding="wav",  # WAV格式
        rate=16000,  # 16kHz采样率
        output_file="example_custom.wav"
    )
    
    print(f"语速: {result.get('speed_ratio', 'N/A')}")
    print(f"编码: {result['encoding']}")
    print(f"音频文件: {result.get('output_file')}")


def example_batch_synthesis():
    """批量合成示例"""
    print("=" * 50)
    print("批量合成示例")
    print("=" * 50)
    
    tts = VolcanoTTS()
    
    # 批量合成多个文本
    texts = [
        "这是第一段文本。",
        "这是第二段文本。",
        "这是第三段文本。"
    ]
    
    results = tts.batch_synthesize(
        texts=texts,
        output_dir="batch_output",
        voice_type="BV001_streaming",
        encoding="mp3"
    )
    
    print(f"批量合成完成，共处理 {len(results)} 个文本")
    for i, result in enumerate(results):
        if result.get('success'):
            print(f"  文本{i+1}: 成功 - {result.get('output_file')}")
        else:
            print(f"  文本{i+1}: 失败 - {result.get('error')}")


def example_file_input():
    """文件输入示例"""
    print("=" * 50)
    print("文件输入示例")
    print("=" * 50)
    
    # 创建示例文本文件
    sample_text = "这是从文件读取的文本内容。火山语音大模型可以将文本转换为自然流畅的语音。"
    with open("sample_text.txt", "w", encoding="utf-8") as f:
        f.write(sample_text)
    
    tts = VolcanoTTS()
    
    # 从文件读取文本并合成
    with open("sample_text.txt", "r", encoding="utf-8") as f:
        text = f.read().strip()
    
    result = tts.synthesize_text(
        text=text,
        output_file="example_from_file.mp3"
    )
    
    print(f"从文件读取文本长度: {result['text_length']} 字符")
    print(f"音频文件: {result.get('output_file')}")
    
    # 清理临时文件
    os.remove("sample_text.txt")


def example_error_handling():
    """错误处理示例"""
    print("=" * 50)
    print("错误处理示例")
    print("=" * 50)
    
    tts = VolcanoTTS()
    
    # 测试各种错误情况
    test_cases = [
        ("", "空文本"),
        ("x" * 2000, "文本过长"),
        ("正常文本", "不支持的音色", {"voice_type": "invalid_voice"}),
        ("正常文本", "不支持的编码", {"encoding": "invalid_encoding"}),
        ("正常文本", "无效语速", {"speed_ratio": 5.0}),
    ]
    
    for i, test_case in enumerate(test_cases):
        text = test_case[0]
        description = test_case[1]
        kwargs = test_case[2] if len(test_case) > 2 else {}
        
        try:
            result = tts.synthesize_text(text=text, **kwargs)
            print(f"测试{i+1} ({description}): 意外成功")
        except Exception as e:
            print(f"测试{i+1} ({description}): 预期错误 - {str(e)}")


def example_voice_list():
    """音色列表示例"""
    print("=" * 50)
    print("音色列表示例")
    print("=" * 50)
    
    tts = VolcanoTTS()
    
    # 获取支持的音色列表
    voices = tts.get_voice_list()
    
    print("支持的音色:")
    for voice_id, voice_name in voices.items():
        print(f"  {voice_id}: {voice_name}")
    
    # 测试每个音色
    test_text = "这是音色测试。"
    for voice_id in voices.keys():
        try:
            result = tts.synthesize_text(
                text=test_text,
                voice_type=voice_id,
                output_file=f"voice_test_{voice_id}.mp3"
            )
            print(f"音色 {voice_id}: 测试成功")
        except Exception as e:
            print(f"音色 {voice_id}: 测试失败 - {str(e)}")


def main():
    """运行所有示例"""
    print("火山语音大模型合成API - 使用示例")
    print("=" * 60)
    
    examples = [
        example_basic_usage,
        example_custom_voice,
        example_custom_parameters,
        example_file_input,
        example_voice_list,
        example_batch_synthesis,
        example_error_handling,
    ]
    
    for example_func in examples:
        try:
            example_func()
            print()
        except Exception as e:
            print(f"示例 {example_func.__name__} 执行失败: {str(e)}")
            print()
    
    print("所有示例执行完成!")


if __name__ == "__main__":
    main()
