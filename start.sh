#!/bin/bash
# 火山语音合成Web应用启动脚本

echo "🚀 启动火山语音合成Web应用..."

# 检查Python环境
if ! command -v python3 &> /dev/null; then
    echo "❌ 错误: 未找到Python3，请先安装Python"
    exit 1
fi

# 检查依赖
echo "📦 检查依赖包..."
pip install -r requirements.txt

# 创建必要的目录
echo "📁 创建目录..."
mkdir -p static/audio
mkdir -p uploads
mkdir -p logs

# 启动应用
echo "🌐 启动Web服务器..."
echo "访问地址: http://localhost:3000"
echo "按 Ctrl+C 停止服务"
echo "----------------------------------------"

python app.py
