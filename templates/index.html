{% extends "base.html" %}

{% block title %}火山语音合成 - 在线文本转语音{% endblock %}

{% block content %}
<div class="row">
    <!-- 左侧：输入区域 -->
    <div class="col-lg-8">
        <div class="card shadow-sm">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0">
                    <i class="fas fa-edit me-2"></i>文本输入
                </h5>
            </div>
            <div class="card-body">
                <form id="tts-form">
                    <!-- 文本输入 -->
                    <div class="mb-3">
                        <label for="text" class="form-label">
                            <i class="fas fa-font me-1"></i>要合成的文本
                            <span class="text-muted">(最大1024字节)</span>
                        </label>
                        <textarea 
                            class="form-control" 
                            id="text" 
                            name="text" 
                            rows="6" 
                            placeholder="请输入要转换为语音的文本..."
                            maxlength="500"
                            required
                        ></textarea>
                        <div class="form-text">
                            <span id="text-length">0</span> / 500 字符
                            (<span id="text-bytes">0</span> / 1024 字节)
                        </div>
                    </div>

                    <!-- 文件上传 -->
                    <div class="mb-3">
                        <label for="text-file" class="form-label">
                            <i class="fas fa-upload me-1"></i>或上传文本文件
                            <span class="text-muted">(与文本输入二选一)</span>
                        </label>
                        <input type="file" class="form-control" id="text-file" accept=".txt">
                        <div class="form-text">
                            支持 .txt 格式文件，上传后将自动使用文件内容进行合成
                            <br>
                            <small class="text-info">
                                <i class="fas fa-info-circle me-1"></i>
                                上传文件后可以切换回文本输入模式进行编辑
                            </small>
                        </div>
                    </div>

                    <!-- 快速文本按钮 -->
                    <div class="mb-3">
                        <label class="form-label">
                            <i class="fas fa-magic me-1"></i>快速文本
                        </label>
                        <div class="btn-group-vertical d-grid gap-2 d-md-block">
                            <button type="button" class="btn btn-outline-secondary btn-sm quick-text" 
                                    data-text="你好，欢迎使用火山语音大模型合成API。">
                                示例文本1
                            </button>
                            <button type="button" class="btn btn-outline-secondary btn-sm quick-text" 
                                    data-text="今天天气很好，适合出门散步。希望大家都有美好的一天。">
                                示例文本2
                            </button>
                            <button type="button" class="btn btn-outline-secondary btn-sm quick-text" 
                                    data-text="人工智能技术正在快速发展，语音合成技术也越来越成熟。">
                                示例文本3
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- 右侧：参数配置 -->
    <div class="col-lg-4">
        <div class="card shadow-sm">
            <div class="card-header bg-success text-white">
                <h5 class="mb-0">
                    <i class="fas fa-cogs me-2"></i>参数配置
                </h5>
            </div>
            <div class="card-body">
                <!-- 音色选择 -->
                <div class="mb-3">
                    <label for="voice_type" class="form-label">
                        <i class="fas fa-user-alt me-1"></i>音色类型
                    </label>
                    <select class="form-select" id="voice_type" name="voice_type">
                        {% for voice_id, voice_name in voices.items() %}
                        <option value="{{ voice_id }}" 
                                {% if voice_id == 'zh_female_xinlingjitang_moon_bigtts' %}selected{% endif %}>
                            {{ voice_name }}
                        </option>
                        {% endfor %}
                    </select>
                </div>

                <!-- 音频格式 -->
                <div class="mb-3">
                    <label for="encoding" class="form-label">
                        <i class="fas fa-file-audio me-1"></i>音频格式
                    </label>
                    <select class="form-select" id="encoding" name="encoding">
                        <option value="mp3" selected>MP3 (推荐)</option>
                        <option value="wav">WAV (高质量)</option>
                        <option value="pcm">PCM (原始)</option>
                        <option value="ogg_opus">OGG Opus</option>
                    </select>
                </div>

                <!-- 语速 -->
                <div class="mb-3">
                    <label for="speed_ratio" class="form-label">
                        <i class="fas fa-tachometer-alt me-1"></i>语速
                        <span class="badge bg-secondary" id="speed-value">1.0</span>
                    </label>
                    <input type="range" class="form-range" id="speed_ratio" name="speed_ratio" 
                           min="0.8" max="2.0" step="0.1" value="1.0">
                    <div class="d-flex justify-content-between">
                        <small class="text-muted">慢 (0.8)</small>
                        <small class="text-muted">快 (2.0)</small>
                    </div>
                </div>

                <!-- 音量 -->
                <div class="mb-3">
                    <label for="loudness_ratio" class="form-label">
                        <i class="fas fa-volume-up me-1"></i>音量
                        <span class="badge bg-secondary" id="volume-value">1.0</span>
                    </label>
                    <input type="range" class="form-range" id="loudness_ratio" name="loudness_ratio" 
                           min="0.5" max="2.0" step="0.1" value="1.0">
                    <div class="d-flex justify-content-between">
                        <small class="text-muted">小 (0.5)</small>
                        <small class="text-muted">大 (2.0)</small>
                    </div>
                </div>

                <!-- 采样率 -->
                <div class="mb-3">
                    <label for="rate" class="form-label">
                        <i class="fas fa-wave-square me-1"></i>采样率
                    </label>
                    <select class="form-select" id="rate" name="rate">
                        <option value="8000">8000 Hz (电话质量)</option>
                        <option value="16000">16000 Hz (标准质量)</option>
                        <option value="24000" selected>24000 Hz (高质量)</option>
                    </select>
                </div>

                <!-- 高级选项 -->
                <div class="mb-3">
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="enable_timestamp" name="enable_timestamp">
                        <label class="form-check-label" for="enable_timestamp">
                            <i class="fas fa-clock me-1"></i>启用时间戳
                        </label>
                    </div>
                </div>

                <!-- 合成按钮 -->
                <div class="d-grid">
                    <button type="submit" class="btn btn-primary btn-lg" id="synthesize-btn" form="tts-form">
                        <i class="fas fa-play me-2"></i>开始合成
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 结果区域 -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card shadow-sm" id="result-card" style="display: none;">
            <div class="card-header bg-info text-white">
                <h5 class="mb-0">
                    <i class="fas fa-music me-2"></i>合成结果
                </h5>
            </div>
            <div class="card-body" id="result-content">
                <!-- 结果内容将通过JavaScript动态填充 -->
            </div>
        </div>
    </div>
</div>

<!-- 加载模态框 -->
<div class="modal fade" id="loading-modal" tabindex="-1" data-bs-backdrop="static" data-bs-keyboard="false">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-body text-center py-4">
                <div class="spinner-border text-primary mb-3" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
                <h5>正在合成语音...</h5>
                <p class="text-muted mb-0">请稍候，这可能需要几秒钟时间</p>
                <div class="progress mt-3">
                    <div class="progress-bar progress-bar-striped progress-bar-animated" 
                         role="progressbar" style="width: 100%"></div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script src="{{ url_for('static', filename='js/tts.js') }}"></script>
{% endblock %}
