{% extends "base.html" %}

{% block title %}火山语音合成 - 在线文本转语音{% endblock %}

{% block content %}
<!-- 传递音色数据到前端 -->
<script>
    // 音色详细信息
    window.voiceDetails = {{ voice_details|tojson }};

    // 音色分类
    window.voiceCategories = {{ categories|tojson }};
</script>
<div class="row">
    <!-- 左侧：输入区域 -->
    <div class="col-lg-8">
        <div class="card shadow-sm">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0">
                    <i class="fas fa-edit me-2"></i>文本输入
                </h5>
            </div>
            <div class="card-body">
                <form id="tts-form">
                    <!-- 文本输入 -->
                    <div class="mb-3">
                        <label for="text" class="form-label">
                            <i class="fas fa-font me-1"></i>要合成的文本
                            <span class="text-muted">(支持长文本自动分段)</span>
                        </label>
                        <textarea
                            class="form-control"
                            id="text"
                            name="text"
                            rows="8"
                            placeholder="请输入要转换为语音的文本...&#10;&#10;💡 提示：&#10;• 支持任意长度文本，系统会自动分段处理&#10;• 超过1024字节会自动分段合成并合并音频&#10;• 建议在句号、感叹号等标点处自然分段"
                            required
                        ></textarea>
                        <div class="form-text">
                            <span id="text-length">0</span> 字符
                            (<span id="text-bytes">0</span> 字节)
                            <span id="segment-info" class="text-info ms-2"></span>
                        </div>
                    </div>

                    <!-- 文件上传 -->
                    <div class="mb-3">
                        <label for="text-file" class="form-label">
                            <i class="fas fa-upload me-1"></i>或上传文本文件
                            <span class="text-muted">(与文本输入二选一)</span>
                        </label>
                        <input type="file" class="form-control" id="text-file" accept=".txt">
                        <div class="form-text">
                            支持 .txt 格式文件，上传后将自动使用文件内容进行合成
                            <br>
                            <small class="text-info">
                                <i class="fas fa-info-circle me-1"></i>
                                支持任意大小文件，系统会自动处理长文本分段
                            </small>
                        </div>
                    </div>

                    <!-- 快速文本按钮 -->
                    <div class="mb-3">
                        <label class="form-label">
                            <i class="fas fa-magic me-1"></i>快速文本
                        </label>
                        <div class="btn-group-vertical d-grid gap-2 d-md-block">
                            <button type="button" class="btn btn-outline-secondary btn-sm quick-text"
                                    data-text="你好，欢迎使用火山语音大模型合成API。这是一个功能强大的文本转语音服务。">
                                欢迎语
                            </button>
                            <button type="button" class="btn btn-outline-secondary btn-sm quick-text"
                                    data-text="今天天气很好，适合出门散步。希望大家都有美好的一天，工作顺利，身体健康。">
                                日常问候
                            </button>
                            <button type="button" class="btn btn-outline-secondary btn-sm quick-text"
                                    data-text="人工智能技术正在快速发展，语音合成技术也越来越成熟，为我们的生活带来了便利。现代AI技术已经能够生成非常自然流畅的语音，在教育、娱乐、无障碍访问等领域发挥着重要作用。随着技术的不断进步，我们相信语音合成将为更多用户带来便利和价值。">
                                科技介绍（长文本示例）
                            </button>
                            <button type="button" class="btn btn-outline-secondary btn-sm quick-text"
                                    data-text="感谢您的使用！如果您有任何问题或建议，请随时联系我们的技术支持团队。">
                                感谢语
                            </button>
                        </div>
                    </div>

                    <!-- 合成按钮 -->
                    <div class="d-grid">
                        <button type="submit" class="btn btn-primary btn-lg" id="synthesize-btn">
                            <i class="fas fa-play me-2"></i>开始合成
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- 右侧：参数配置 -->
    <div class="col-lg-4">
        <div class="card shadow-sm">
            <div class="card-header bg-success text-white">
                <h5 class="mb-0">
                    <i class="fas fa-cogs me-2"></i>参数配置
                </h5>
            </div>
            <div class="card-body">
                <!-- 音色选择 -->
                <div class="mb-3">
                    <label for="voice_type" class="form-label">
                        <i class="fas fa-user-alt me-1"></i>音色类型
                        <button type="button" class="btn btn-sm btn-outline-info ms-2" data-bs-toggle="modal" data-bs-target="#voiceModal">
                            <i class="fas fa-list me-1"></i>查看全部
                        </button>
                    </label>
                    <select class="form-select" id="voice_type" name="voice_type" form="tts-form">
                        {% for voice_id, voice_name in voices.items() %}
                        <option value="{{ voice_id }}"
                                {% if voice_id == 'zh_female_xinlingjitang_moon_bigtts' %}selected{% endif %}>
                            {{ voice_name }}
                        </option>
                        {% endfor %}
                    </select>
                    <div class="form-text">
                        <small id="voice-description" class="text-muted">心灵鸡汤女声 - 温暖治愈，适合情感类内容</small>
                    </div>
                </div>
                <!-- 音频格式 -->
                <div class="mb-3">
                    <label for="encoding" class="form-label">
                        <i class="fas fa-file-audio me-1"></i>音频格式
                    </label>
                    <select class="form-select" id="encoding" name="encoding" form="tts-form">
                        <option value="mp3" selected>MP3 (推荐)</option>
                        <option value="wav">WAV (高质量)</option>
                        <option value="pcm">PCM (原始)</option>
                        <option value="ogg_opus">OGG Opus</option>
                    </select>
                </div>

                <!-- 语速 -->
                <div class="mb-3">
                    <label for="speed_ratio" class="form-label">
                        <i class="fas fa-tachometer-alt me-1"></i>语速
                        <span class="badge bg-secondary" id="speed-value">1.0</span>
                    </label>
                    <input type="range" class="form-range" id="speed_ratio" name="speed_ratio" form="tts-form"
                           min="0.8" max="2.0" step="0.1" value="1.0">
                    <div class="d-flex justify-content-between">
                        <small class="text-muted">慢 (0.8)</small>
                        <small class="text-muted">快 (2.0)</small>
                    </div>
                </div>

                <!-- 音量 -->
                <div class="mb-3">
                    <label for="loudness_ratio" class="form-label">
                        <i class="fas fa-volume-up me-1"></i>音量
                        <span class="badge bg-secondary" id="volume-value">1.0</span>
                    </label>
                    <input type="range" class="form-range" id="loudness_ratio" name="loudness_ratio" form="tts-form"
                           min="0.5" max="2.0" step="0.1" value="1.0">
                    <div class="d-flex justify-content-between">
                        <small class="text-muted">小 (0.5)</small>
                        <small class="text-muted">大 (2.0)</small>
                    </div>
                </div>

                <!-- 采样率 -->
                <div class="mb-3">
                    <label for="rate" class="form-label">
                        <i class="fas fa-wave-square me-1"></i>采样率
                    </label>
                    <select class="form-select" id="rate" name="rate" form="tts-form">
                        <option value="8000">8000 Hz (电话质量)</option>
                        <option value="16000">16000 Hz (标准质量)</option>
                        <option value="24000" selected>24000 Hz (高质量)</option>
                    </select>
                </div>

                <!-- 长文本处理 -->
                <div class="mb-3">
                    <label class="form-label">
                        <i class="fas fa-cut me-1"></i>长文本处理
                    </label>
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="auto_segment" name="auto_segment" form="tts-form" checked>
                        <label class="form-check-label" for="auto_segment">
                            自动分段处理长文本
                        </label>
                    </div>
                    <div class="form-text">
                        <small class="text-muted">超过1024字节时自动分段合成并合并音频</small>
                    </div>
                </div>

                <!-- 高级选项 -->
                <div class="mb-3">
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="enable_timestamp" name="enable_timestamp" form="tts-form">
                        <label class="form-check-label" for="enable_timestamp">
                            <i class="fas fa-clock me-1"></i>启用时间戳
                        </label>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 结果区域 -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card shadow-sm" id="result-card" style="display: none;">
            <div class="card-header bg-info text-white">
                <h5 class="mb-0">
                    <i class="fas fa-music me-2"></i>合成结果
                </h5>
            </div>
            <div class="card-body" id="result-content">
                <!-- 结果内容将通过JavaScript动态填充 -->
            </div>
        </div>
    </div>
</div>

<!-- 加载模态框 -->
<div class="modal fade" id="loading-modal" tabindex="-1" data-bs-backdrop="static" data-bs-keyboard="false">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-body text-center py-4">
                <div class="spinner-border text-primary mb-3" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
                <h5>正在合成语音...</h5>
                <p class="text-muted mb-0">请稍候，这可能需要几秒钟时间</p>
                <div class="progress mt-3">
                    <div class="progress-bar progress-bar-striped progress-bar-animated" 
                         role="progressbar" style="width: 100%"></div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 音色选择模态框 -->
<div class="modal fade" id="voiceModal" tabindex="-1" aria-labelledby="voiceModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="voiceModalLabel">
                    <i class="fas fa-user-alt me-2"></i>选择音色
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-3">
                        <div class="list-group" id="voice-categories">
                            <button type="button" class="list-group-item list-group-item-action active" data-category="general">
                                <i class="fas fa-star me-2"></i>通用场景
                            </button>
                            <button type="button" class="list-group-item list-group-item-action" data-category="roleplay">
                                <i class="fas fa-theater-masks me-2"></i>角色扮演
                            </button>
                            <button type="button" class="list-group-item list-group-item-action" data-category="accent">
                                <i class="fas fa-map-marker-alt me-2"></i>趣味口音
                            </button>
                            <button type="button" class="list-group-item list-group-item-action" data-category="video">
                                <i class="fas fa-video me-2"></i>视频配音
                            </button>
                            <button type="button" class="list-group-item list-group-item-action" data-category="multilang">
                                <i class="fas fa-globe me-2"></i>多语种
                            </button>
                            <button type="button" class="list-group-item list-group-item-action" data-category="special">
                                <i class="fas fa-crown me-2"></i>特色专业
                            </button>
                            <button type="button" class="list-group-item list-group-item-action" data-category="narration">
                                <i class="fas fa-microphone me-2"></i>解说配音
                            </button>
                        </div>
                    </div>
                    <div class="col-md-9">
                        <div id="voice-list" class="row g-3">
                            <!-- 音色列表将通过JavaScript动态加载 -->
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" id="confirm-voice-selection">确认选择</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script src="{{ url_for('static', filename='js/tts.js') }}"></script>
{% endblock %}
